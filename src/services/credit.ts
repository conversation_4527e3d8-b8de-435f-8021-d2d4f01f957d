import {
  findCreditByOrderNo,
  getUserValidCredits,
  insertCredit,
} from "@/models/credit";
import { credits as creditsTable } from "@/db/schema";
import { getIsoTimestr } from "@/lib/time";
import { getSnowId } from "@/lib/hash";
import { Order } from "@/types/order";
import { UserCredits } from "@/types/user";
import { getFirstPaidOrderByUserUuid } from "@/models/order";

export enum CreditsTransType {
  NewUser = "new_user", // initial credits for new user
  OrderPay = "order_pay", // user pay for credits
  SystemAdd = "system_add", // system add credits
  Ping = "ping", // cost for ping api
  RemoveBackground = "remove_background", // cost for remove background api
  ChangeBackground = "change_background", // cost for change background api
}

export enum CreditsAmount {
  NewUserGet = 10,
}

// 积分消耗常量 - 避免枚举重复值问题
export const CREDITS_COST = {
  PING: 1,
  REMOVE_BACKGROUND: 1,
  CHANGE_BACKGROUND: 2,
} as const;

export async function getUserCredits(user_uuid: string): Promise<UserCredits> {
  let user_credits: UserCredits = {
    left_credits: 0,
  };

  try {
    console.log(`[DEBUG] getUserCredits called for user: ${user_uuid}`);

    const first_paid_order = await getFirstPaidOrderByUserUuid(user_uuid);
    if (first_paid_order) {
      user_credits.is_recharged = true;
    }

    const credits = await getUserValidCredits(user_uuid);
    console.log(`[DEBUG] getUserCredits - getUserValidCredits returned:`, credits);

    if (credits) {
      credits.forEach((v) => {
        console.log(`[DEBUG] getUserCredits - processing record:`, {
          id: v.id,
          credits: v.credits,
          expired_at: v.expired_at,
          trans_type: v.trans_type
        });
        user_credits.left_credits += v.credits || 0;
      });
    }

    console.log(`[DEBUG] getUserCredits - calculated left_credits before adjustment: ${user_credits.left_credits}`);

    if (user_credits.left_credits < 0) {
      user_credits.left_credits = 0;
    }

    if (user_credits.left_credits > 0) {
      user_credits.is_pro = true;
    }

    console.log(`[DEBUG] getUserCredits - final result:`, user_credits);
    return user_credits;
  } catch (e) {
    console.log("get user credits failed: ", e);
    return user_credits;
  }
}

export async function decreaseCredits({
  user_uuid,
  trans_type,
  credits,
}: {
  user_uuid: string;
  trans_type: CreditsTransType;
  credits: number;
}) {
  try {
    let order_no = "";
    let expired_at = "";
    let left_credits = 0;

    console.log(`[DEBUG] decreaseCredits called for user: ${user_uuid}, credits needed: ${credits}`);

    const userCredits = await getUserValidCredits(user_uuid);
    console.log(`[DEBUG] getUserValidCredits returned:`, userCredits);

    if (userCredits) {
      for (let i = 0, l = userCredits.length; i < l; i++) {
        const credit = userCredits[i];
        console.log(`[DEBUG] Processing credit record:`, {
          id: credit.id,
          credits: credit.credits,
          expired_at: credit.expired_at,
          trans_type: credit.trans_type
        });

        left_credits += credit.credits;
        console.log(`[DEBUG] Running total credits: ${left_credits}`);

        // credit enough for cost
        if (left_credits >= credits) {
          order_no = credit.order_no || "";
          expired_at = credit.expired_at?.toISOString() || "";
          console.log(`[DEBUG] Found sufficient credits, order_no: ${order_no}, expired_at: ${expired_at}`);
          break;
        }

        // look for next credit
      }
    }

    // 🔧 修复：与 getUserCredits 保持一致，负数积分重置为0
    if (left_credits < 0) {
      console.log(`[DEBUG] Negative credits detected (${left_credits}), resetting to 0`);
      left_credits = 0;
    }

    console.log(`[DEBUG] Final calculation - left_credits: ${left_credits}, required: ${credits}`);

    // Check if user has enough credits before deducting
    if (left_credits < credits) {
      console.log(`[DEBUG] Insufficient credits detected, throwing error`);
      throw new Error(`Insufficient credits. Required: ${credits}, Available: ${left_credits}`);
    }

    // Fix: Handle empty expired_at properly
    const expiredAtDate = expired_at ? new Date(expired_at) : null;
    console.log(`[DEBUG] Creating credit record with expired_at:`, expiredAtDate);

    const new_credit: typeof creditsTable.$inferInsert = {
      trans_no: getSnowId(),
      created_at: new Date(getIsoTimestr()),
      expired_at: expiredAtDate,
      user_uuid: user_uuid,
      trans_type: trans_type,
      credits: 0 - credits,
      order_no: order_no,
    };

    console.log(`[DEBUG] Inserting credit record:`, new_credit);
    await insertCredit(new_credit);
    console.log(`[DEBUG] Credit deduction completed successfully`);
  } catch (e) {
    console.log("decrease credits failed: ", e);
    throw e;
  }
}

export async function increaseCredits({
  user_uuid,
  trans_type,
  credits,
  expired_at,
  order_no,
}: {
  user_uuid: string;
  trans_type: string;
  credits: number;
  expired_at?: string;
  order_no?: string;
}) {
  try {
    const new_credit: typeof creditsTable.$inferInsert = {
      trans_no: getSnowId(),
      created_at: new Date(getIsoTimestr()),
      user_uuid: user_uuid,
      trans_type: trans_type,
      credits: credits,
      order_no: order_no || "",
      expired_at: expired_at ? new Date(expired_at) : null,
    };
    await insertCredit(new_credit);
  } catch (e) {
    console.log("increase credits failed: ", e);
    throw e;
  }
}

export async function updateCreditForOrder(order: Order) {
  try {
    const credit = await findCreditByOrderNo(order.order_no);
    if (credit) {
      // order already increased credit
      return;
    }

    await increaseCredits({
      user_uuid: order.user_uuid,
      trans_type: CreditsTransType.OrderPay,
      credits: order.credits,
      expired_at: order.expired_at,
      order_no: order.order_no,
    });
  } catch (e) {
    console.log("update credit for order failed: ", e);
    throw e;
  }
}
