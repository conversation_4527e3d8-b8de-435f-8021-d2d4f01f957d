"use client";

import { useState, useRef, DragEvent, ChangeEvent, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { Upload, X, Image as ImageIcon, CreditCard, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAppContext } from '@/contexts/app';
import { useFileUploadContext } from '@/contexts/file-upload';
import { compressImage, needsSmartCompression, formatFileSize } from '@/lib/image-compression';
import { useUserCredits } from '@/hooks/useUserCredits';

interface RemoveBackgroundResult {
  original: {
    url: string;
    filename: string;
    size: number;
    type: string;
  };
  processed: {
    url: string;
    filename: string;
    key: string;
  };
  segmentType: string;
  requestId: string;
}

interface FileUploadProps {
  onFileSelect: (file: File) => void;
  onUploadComplete: (result: RemoveBackgroundResult) => void;
  segmentType?: 'common' | 'body' | 'hair';
  returnForm?: 'cutOut' | 'whiteBK';
  disabled?: boolean;
}

export default function FileUpload({
  onFileSelect,
  onUploadComplete,
  segmentType = 'common',
  returnForm = 'cutOut', // Default to transparent background
  disabled = false
}: FileUploadProps) {
  const { user, setShowSignModal } = useAppContext();
  const { removeBackgroundState, setRemoveBackgroundState } = useFileUploadContext();
  const { credits, hasEnoughCredits, refreshCredits } = useUserCredits();

  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Use state from context
  const selectedFile = removeBackgroundState.selectedFile;
  const previewUrl = removeBackgroundState.previewUrl;

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Restore file state when component mounts
  useEffect(() => {
    if (selectedFile) {
      onFileSelect(selectedFile);
    }
  }, [selectedFile, onFileSelect]);

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragging(true);
    }
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (disabled) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelection(files[0]);
    }
  };

  const handleFileInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelection(files[0]);
    }
  };

  const handleFileSelection = async (file: File) => {
    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Invalid file type. Only JPEG, PNG, and WebP are supported.');
      return;
    }

    let processedFile = file;

    // Check if smart compression is needed
    if (needsSmartCompression(file)) {
      toast.info(`Optimizing image quality (${formatFileSize(file.size)})...`);
      try {
        processedFile = await compressImage(file, {
          maxWidth: 2048, // 更大尺寸保持质量
          maxHeight: 2048,
          quality: 0.95, // 更高质量
          maxSizeMB: 8,
          preserveFormat: true, // 保持原始格式
          minCompressionRatio: 0.2 // 确保有效压缩
        });
        toast.success(`Image optimized to ${formatFileSize(processedFile.size)}`);
      } catch (error) {
        console.error('Image optimization failed:', error);
        toast.error('Failed to optimize image. Please try a smaller file.');
        return;
      }
    }

    // Final size check after compression
    const maxSize = 8 * 1024 * 1024; // 8MB
    if (processedFile.size > maxSize) {
      toast.error('File size too large even after optimization. Please use a smaller image.');
      return;
    }

    // 创建预览URL
    const url = URL.createObjectURL(processedFile);

    // Update context state
    setRemoveBackgroundState({
      selectedFile: processedFile,
      previewUrl: url,
      returnForm: returnForm,
    });

    onFileSelect(processedFile);
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error('Please select a file first');
      return;
    }

    // Check if user is logged in
    if (!user) {
      toast.error('Please login to use remove background feature');
      setShowSignModal(true);
      return;
    }

    // Check if user has enough credits (1 credit for remove background)
    if (!hasEnoughCredits(1)) {
      toast.error('Insufficient credits. Please purchase more credits to use this feature.');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('segmentType', segmentType);
      formData.append('returnForm', returnForm);

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      const response = await fetch('/api/remove-background', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.code !== 0) {
        throw new Error(result.message || 'Upload failed');
      }

      toast.success('Background removed successfully!');
      onUploadComplete(result.data);

      // Refresh credits after successful upload
      refreshCredits();

    } catch (error: any) {
      console.error('Upload error:', error);
      toast.error(error.message || 'Failed to remove background');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleRemoveFile = () => {
    // Clean up preview URL
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }

    // Clear context state
    setRemoveBackgroundState({
      selectedFile: null,
      previewUrl: null,
      returnForm: returnForm,
    });

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      <Card className="border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors">
        <CardContent className="p-6">
          {!selectedFile ? (
            <div
              className={cn(
                "flex flex-col items-center justify-center py-12 cursor-pointer transition-colors",
                isDragging && "bg-blue-50 border-blue-300",
                disabled && "opacity-50 cursor-not-allowed"
              )}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={handleClick}
            >
              <Upload className="w-12 h-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold text-gray-700 mb-2">
                Upload your image
              </h3>
              <p className="text-gray-500 text-center mb-4">
                Drag and drop your image here, or click to browse
              </p>
              <p className="text-sm text-gray-400">
                Supports JPEG, PNG, WebP • Max 10MB
              </p>
              
              <input
                ref={fileInputRef}
                type="file"
                accept="image/jpeg,image/jpg,image/png,image/webp"
                onChange={handleFileInputChange}
                className="hidden"
                disabled={disabled}
              />
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-start space-x-4">
                {previewUrl && (
                  <div className="flex-shrink-0">
                    <img
                      src={previewUrl}
                      alt="Preview"
                      className="w-20 h-20 object-cover rounded-lg border"
                    />
                  </div>
                )}
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {selectedFile.name}
                      </p>
                      <p className="text-sm text-gray-500">
                        {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleRemoveFile}
                      disabled={isUploading}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                  
                  {isUploading && (
                    <div className="mt-2">
                      <Progress value={uploadProgress} className="h-2" />
                      <p className="text-xs text-gray-500 mt-1">
                        Processing... {uploadProgress}%
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Credits display */}
              {user && credits && (
                <div className="flex items-center justify-between text-sm text-muted-foreground mb-2">
                  <div className="flex items-center gap-1">
                    <CreditCard className="h-4 w-4" />
                    <span>Credits: {credits.left_credits}</span>
                  </div>
                  <span>Cost: 1 credit</span>
                </div>
              )}

              {/* Warning for insufficient credits */}
              {user && credits && !hasEnoughCredits(1) && (
                <div className="flex items-center gap-2 p-3 bg-orange-50 border border-orange-200 rounded-md mb-2">
                  <AlertCircle className="h-4 w-4 text-orange-600" />
                  <span className="text-sm text-orange-700">
                    Insufficient credits. You need 1 credit to remove background.
                  </span>
                </div>
              )}

              <Button
                onClick={handleUpload}
                disabled={isUploading || disabled || (user && credits && !hasEnoughCredits(1))}
                className="w-full"
              >
                {isUploading ? 'Removing Background...' : 'Remove Background'}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
