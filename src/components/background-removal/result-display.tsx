"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Download, RotateCcw, Share2 } from 'lucide-react';
import { toast } from 'sonner';

interface ResultDisplayProps {
  result: {
    original: {
      url: string;
      filename: string;
      size: number;
      type: string;
    };
    processed: {
      url: string;
      filename: string;
      key: string;
    };
    segmentType: string;
    requestId: string;
  };
  onReset: () => void;
}

export default function ResultDisplay({ result, onReset }: ResultDisplayProps) {
  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownload = async () => {
    setIsDownloading(true);
    try {
      // Use our proxy API to download the image
      const downloadUrl = `/api/download-image?url=${encodeURIComponent(result.processed.url)}&filename=${encodeURIComponent(result.processed.filename || 'background-removed.png')}`;

      // Create a temporary link to trigger download
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = result.processed.filename || 'background-removed.png';
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      toast.success('Image download started!');
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download image');
    } finally {
      setIsDownloading(false);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'AI Background Removal Result',
          text: 'Check out this background removal result!',
          url: result.processed.url,
        });
      } catch (error) {
        console.error('Share error:', error);
        // Fallback to copying URL
        copyToClipboard();
      }
    } else {
      copyToClipboard();
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(result.processed.url).then(() => {
      toast.success('Image URL copied to clipboard!');
    }).catch(() => {
      toast.error('Failed to copy URL');
    });
  };

  const getSegmentTypeLabel = (type: string) => {
    switch (type) {
      case 'body':
        return 'Human Portrait';
      case 'hair':
        return 'Hair Segmentation';
      case 'common':
      default:
        return 'General Removal';
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Background Removal Complete
          </h2>
          <p className="text-gray-600 mt-1">
            Your image has been processed successfully
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Badge variant="secondary">
            {getSegmentTypeLabel(result.segmentType)}
          </Badge>
        </div>
      </div>

      {/* Before/After Comparison */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Original Image */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Original</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
              <img
                src={result.original.url}
                alt="Original image"
                className="w-full h-full object-contain"
              />
            </div>
            <div className="mt-3 text-sm text-gray-500">
              <p>{result.original.filename}</p>
              <p>{(result.original.size / 1024 / 1024).toFixed(2)} MB</p>
            </div>
          </CardContent>
        </Card>

        {/* Processed Image */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Background Removed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden relative">
              {/* Checkerboard pattern for transparency */}
              <div 
                className="absolute inset-0 opacity-20"
                style={{
                  backgroundImage: `
                    linear-gradient(45deg, #ccc 25%, transparent 25%), 
                    linear-gradient(-45deg, #ccc 25%, transparent 25%), 
                    linear-gradient(45deg, transparent 75%, #ccc 75%), 
                    linear-gradient(-45deg, transparent 75%, #ccc 75%)
                  `,
                  backgroundSize: '20px 20px',
                  backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px'
                }}
              />
              <img
                src={result.processed.url}
                alt="Background removed"
                className="w-full h-full object-contain relative z-10"
              />
            </div>
            <div className="mt-3 text-sm text-gray-500">
              <p>{result.processed.filename}</p>
              <p>PNG format with transparency</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        <Button
          onClick={handleDownload}
          disabled={isDownloading}
          className="flex items-center space-x-2"
        >
          <Download className="w-4 h-4" />
          <span>{isDownloading ? 'Downloading...' : 'Download Result'}</span>
        </Button>
        
        <Button
          variant="outline"
          onClick={handleShare}
          className="flex items-center space-x-2"
        >
          <Share2 className="w-4 h-4" />
          <span>Share</span>
        </Button>
        
        <Button
          variant="outline"
          onClick={onReset}
          className="flex items-center space-x-2"
        >
          <RotateCcw className="w-4 h-4" />
          <span>Process Another Image</span>
        </Button>
      </div>

      {/* Technical Details */}
      <Card className="bg-gray-50">
        <CardContent className="pt-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <p className="font-medium text-gray-700">Processing Type</p>
              <p className="text-gray-600">{getSegmentTypeLabel(result.segmentType)}</p>
            </div>
            <div>
              <p className="font-medium text-gray-700">Output Format</p>
              <p className="text-gray-600">PNG with transparency</p>
            </div>
            <div>
              <p className="font-medium text-gray-700">Request ID</p>
              <p className="text-gray-600 font-mono text-xs">{result.requestId}</p>
            </div>
            <div>
              <p className="font-medium text-gray-700">Status</p>
              <p className="text-green-600">✓ Completed</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
