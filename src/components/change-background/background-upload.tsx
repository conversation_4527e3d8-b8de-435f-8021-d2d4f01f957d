"use client";

import { useState, useRef, Drag<PERSON><PERSON>, ChangeEvent, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { Upload, X, CreditCard, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAppContext } from '@/contexts/app';
import { useFileUploadContext } from '@/contexts/file-upload';
import { compressImage, needsSmartCompression, formatFileSize, prepareDashScopeImage } from '@/lib/image-compression';
import { useUserCredits } from '@/hooks/useUserCredits';

interface BackgroundChangeResult {
  original: {
    url: string;
    filename: string;
    size: number;
    type: string;
  };
  removedBackground: {
    url: string;
    filename: string;
    key: string;
  };
  final: {
    url: string;
    filename: string;
    key: string;
  };
  parameters: {
    backgroundType: string;
    prompt?: string;
    referenceImageUrl?: string;
    segmentType: string;
  };
  requestId: string;
}

interface BackgroundUploadProps {
  onFileSelect: (file: File) => void;
  onUploadComplete: (result: BackgroundChangeResult) => void;
  disabled?: boolean;
}

// 类型定义已移除，因为我们使用固定值

export default function BackgroundUpload({
  onFileSelect,
  onUploadComplete,
  disabled = false
}: BackgroundUploadProps) {
  const { user, setShowSignModal } = useAppContext();
  const { changeBackgroundState, setChangeBackgroundState } = useFileUploadContext();
  const { credits, hasEnoughCredits, refreshCredits } = useUserCredits();

  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Use state from context
  const selectedFile = changeBackgroundState.selectedFile;
  const previewUrl = changeBackgroundState.previewUrl;
  const prompt = changeBackgroundState.prompt || '';

  // 背景生成参数 - 固定使用text模式和common分割
  const backgroundType = 'text'; // 固定使用Text-Guided模式
  const segmentType = 'common';   // 固定使用General分割
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Restore file state when component mounts
  useEffect(() => {
    if (selectedFile) {
      onFileSelect(selectedFile);
    }
  }, [selectedFile, onFileSelect]);

  // 不再需要backgroundTypes和segmentTypes定义，因为我们使用固定值

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragging(true);
    }
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (disabled) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelection(files[0]);
    }
  };

  const handleFileInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelection(files[0]);
    }
  };

  const handleFileSelection = async (file: File) => {
    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Invalid file type. Only JPEG, PNG, and WebP are supported.');
      return;
    }

    // 从环境变量读取压缩配置
    const compressionEnabled = process.env.NEXT_PUBLIC_CHANGE_BACKGROUND_COMPRESSION_ENABLED !== 'false';
    const maxSizeMB = parseInt(process.env.NEXT_PUBLIC_CHANGE_BACKGROUND_MAX_SIZE_MB || '6');
    const quality = parseFloat(process.env.NEXT_PUBLIC_CHANGE_BACKGROUND_COMPRESSION_QUALITY || '0.92');
    const maxWidth = parseInt(process.env.NEXT_PUBLIC_CHANGE_BACKGROUND_MAX_WIDTH || '2048');
    const maxHeight = parseInt(process.env.NEXT_PUBLIC_CHANGE_BACKGROUND_MAX_HEIGHT || '2048');

    let processedFile = file;

    // 只有在启用压缩且需要压缩时才进行压缩
    if (compressionEnabled && needsSmartCompression(file)) {
      toast.info(`Optimizing image quality (${formatFileSize(file.size)})...`);
      try {
        processedFile = await compressImage(file, {
          maxWidth,
          maxHeight,
          quality,
          maxSizeMB,
          preserveFormat: true,
          minCompressionRatio: 0.15 // 保持清晰度，避免过度压缩
        });
        toast.success(`Image optimized to ${formatFileSize(processedFile.size)}`);
      } catch (error) {
        console.error('Image optimization failed:', error);
        toast.error('Failed to optimize image. Using original file.');
        processedFile = file; // 压缩失败时使用原文件
      }
    } else if (!compressionEnabled) {
      console.log('Image compression disabled by configuration');
    }

    // Final size check after compression
    const maxSize = 8 * 1024 * 1024; // 8MB
    if (processedFile.size > maxSize) {
      toast.error('File size too large even after optimization. Please use a smaller image.');
      return;
    }

    // 创建预览URL
    const url = URL.createObjectURL(processedFile);

    // Update context state
    setChangeBackgroundState({
      selectedFile: processedFile,
      previewUrl: url,
      prompt: changeBackgroundState.prompt || '',
    });

    onFileSelect(processedFile);
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error('Please select a file first');
      return;
    }

    // Check if user is logged in
    if (!user) {
      toast.error('Please login to use change background feature');
      setShowSignModal(true);
      return;
    }

    // Check if user has enough credits (2 credits for change background)
    if (!hasEnoughCredits(2)) {
      toast.error('Insufficient credits. Please purchase more credits to use this feature.');
      return;
    }

    // 验证必需参数 - 只需要验证prompt，因为我们使用text模式
    if (!prompt.trim()) {
      toast.error('Please enter a background description');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('backgroundType', backgroundType);
      formData.append('segmentType', segmentType);
      
      if (prompt.trim()) {
        formData.append('prompt', prompt.trim());
      }

      // 不再需要referenceImageUrl，因为我们只使用text模式

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 5;
        });
      }, 1000);

      const response = await fetch('/api/change-background', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.code !== 0) {
        throw new Error(result.message || 'Upload failed');
      }

      toast.success('Background changed successfully!');
      onUploadComplete(result.data);

      // Refresh credits after successful upload
      refreshCredits();

    } catch (error: unknown) {
      console.error('Upload error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to change background';
      toast.error(errorMessage);
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleRemoveFile = () => {
    // Clean up preview URL
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }

    // Clear context state
    setChangeBackgroundState({
      selectedFile: null,
      previewUrl: null,
      prompt: changeBackgroundState.prompt || '',
    });

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* File Upload - 移到最前面 */}
      <Card className="border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors">
        <CardContent className="p-6">
          {!selectedFile ? (
            <div
              className={cn(
                "flex flex-col items-center justify-center py-12 cursor-pointer transition-colors",
                isDragging && "bg-blue-50 border-blue-300",
                disabled && "opacity-50 cursor-not-allowed"
              )}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={handleClick}
            >
              <Upload className="w-12 h-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold text-gray-700 mb-2">
                Upload your image
              </h3>
              <p className="text-gray-500 text-center mb-4">
                Drag and drop your image here, or click to browse
              </p>
              <p className="text-sm text-gray-400">
                Supports JPEG, PNG, WebP • Max 10MB
              </p>
              
              <input
                ref={fileInputRef}
                type="file"
                accept="image/jpeg,image/jpg,image/png,image/webp"
                onChange={handleFileInputChange}
                className="hidden"
                disabled={disabled}
              />
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-start space-x-4">
                {previewUrl && (
                  <div className="flex-shrink-0">
                    <img
                      src={previewUrl}
                      alt="Preview"
                      className="w-20 h-20 object-cover rounded-lg border"
                    />
                  </div>
                )}
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {selectedFile.name}
                      </p>
                      <p className="text-sm text-gray-500">
                        {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleRemoveFile}
                      disabled={isUploading}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                  
                  {isUploading && (
                    <div className="mt-2">
                      <Progress value={uploadProgress} className="h-2" />
                      <p className="text-xs text-gray-500 mt-1">
                        Processing... {uploadProgress}%
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Credits display */}
              {user && credits && (
                <div className="flex items-center justify-between text-sm text-muted-foreground mb-2">
                  <div className="flex items-center gap-1">
                    <CreditCard className="h-4 w-4" />
                    <span>Credits: {credits.left_credits}</span>
                  </div>
                  <span>Cost: 2 credits</span>
                </div>
              )}

              {/* Warning for insufficient credits */}
              {user && credits && !hasEnoughCredits(2) && (
                <div className="flex items-center gap-2 p-3 bg-orange-50 border border-orange-200 rounded-md mb-2">
                  <AlertCircle className="h-4 w-4 text-orange-600" />
                  <span className="text-sm text-orange-700">
                    Insufficient credits. You need 2 credits to change background.
                  </span>
                </div>
              )}

              <Button
                onClick={handleUpload}
                disabled={isUploading || disabled || (user && credits && !hasEnoughCredits(2))}
                className="w-full"
              >
                {isUploading ? 'Changing Background...' : 'Change Background'}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Parameters - 移到文件上传后面 */}
      <Card>
        <CardHeader>
          <CardTitle>Describe the background you want</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Segment Type - Hidden, using General by default */}
          {/*
          <div>
            <Label className="text-sm font-medium">Subject Detection Method</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {segmentTypes.map((type) => (
                <Badge
                  key={type.id}
                  variant={segmentType === type.id ? "default" : "outline"}
                  className="cursor-pointer"
                  onClick={() => setSegmentType(type.id)}
                >
                  {type.name}
                </Badge>
              ))}
            </div>
          </div>
          */}

          {/* Text Prompt - Always show since we're using text mode by default */}
          <div>
            <Label htmlFor="prompt">Background Description</Label>
            <Textarea
              id="prompt"
              placeholder="Describe the background you want (e.g., 'mountain landscape with sunset', 'modern office interior', 'tropical beach scene')"
              value={prompt}
              onChange={(e) => setChangeBackgroundState({
                ...changeBackgroundState,
                prompt: e.target.value,
              })}
              className="mt-1"
              rows={3}
            />
            <p className="text-xs text-gray-500 mt-1">
              English: max 150 words | Chinese: max 100-120 characters
            </p>
          </div>

          {/* Reference Image URL - Hidden since we're using text-only mode */}
          {/*
          {(backgroundType === 'image' || backgroundType === 'text_image') && (
            <div>
              <Label htmlFor="referenceImageUrl">Reference Image URL</Label>
              <Input
                id="referenceImageUrl"
                type="url"
                placeholder="https://example.com/reference-image.jpg"
                value={referenceImageUrl}
                onChange={(e) => setReferenceImageUrl(e.target.value)}
                className="mt-1"
              />
              <p className="text-xs text-gray-500 mt-1">
                URL of an image to use as style reference
              </p>
            </div>
          )}
          */}
        </CardContent>
      </Card>
    </div>
  );
}
