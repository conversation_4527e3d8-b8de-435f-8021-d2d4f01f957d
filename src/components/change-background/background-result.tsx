"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Download, RotateCcw, Share2, Eye, EyeOff } from 'lucide-react';
import { toast } from 'sonner';

interface BackgroundResultProps {
  result: {
    original: {
      url: string;
      filename: string;
      size: number;
      type: string;
    };
    removedBackground: {
      url: string;
      filename: string;
      key: string;
    };
    final: {
      url: string;
      filename: string;
      key: string;
    };
    parameters: {
      backgroundType: string;
      prompt?: string;
      referenceImageUrl?: string;
      segmentType: string;
    };
    requestId: string;
  };
  onReset: () => void;
}

export default function BackgroundResult({ result, onReset }: BackgroundResultProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const [showSteps, setShowSteps] = useState(false);

  const handleDownload = async () => {
    setIsDownloading(true);
    try {
      // Directly use the base64 data from the result for download
      const imageUrl = result.final.url;
      const filename = result.final.filename || 'background-changed.png';

      // Check if it's a base64 data URL
      if (imageUrl.startsWith('data:')) {
        // Direct base64 download - no need for API call
        const a = document.createElement('a');
        a.href = imageUrl;
        a.download = filename;
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        toast.success('Image download started!');
      } else {
        // Fallback to API for non-base64 URLs (if any)
        const downloadUrl = `/api/download-image?url=${encodeURIComponent(imageUrl)}&filename=${encodeURIComponent(filename)}`;

        const a = document.createElement('a');
        a.href = downloadUrl;
        a.download = filename;
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        toast.success('Image download started!');
      }
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download image');
    } finally {
      setIsDownloading(false);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'AI Background Change Result',
          text: 'Check out this amazing background change result!',
          url: result.final.url,
        });
      } catch (error) {
        console.error('Share error:', error);
        copyToClipboard();
      }
    } else {
      copyToClipboard();
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(result.final.url).then(() => {
      toast.success('Image URL copied to clipboard!');
    }).catch(() => {
      toast.error('Failed to copy URL');
    });
  };

  const getBackgroundTypeLabel = (type: string) => {
    switch (type) {
      case 'text':
        return 'Text-Guided';
      case 'image':
        return 'Image-Guided';
      case 'text_image':
        return 'Text + Image Guided';
      default:
        return 'Unknown';
    }
  };

  const getSegmentTypeLabel = (type: string) => {
    switch (type) {
      case 'body':
        return 'Human Portrait';
      case 'hair':
        return 'Hair Segmentation';
      case 'common':
      default:
        return 'General Removal';
    }
  };

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Background Change Complete
          </h2>
          <p className="text-gray-600 mt-1">
            Your image has been processed with a new background
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Badge variant="secondary">
            {getBackgroundTypeLabel(result.parameters.backgroundType)}
          </Badge>
          <Badge variant="outline">
            {getSegmentTypeLabel(result.parameters.segmentType)}
          </Badge>
        </div>
      </div>

      {/* Before/After Comparison */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Original Image */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Original Image</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
              <img
                src={result.original.url}
                alt="Original image"
                className="w-full h-full object-contain"
              />
            </div>
            <div className="mt-3 text-sm text-gray-500">
              <p>{result.original.filename}</p>
              <p>{(result.original.size / 1024 / 1024).toFixed(2)} MB</p>
            </div>
          </CardContent>
        </Card>

        {/* Final Result */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">New Background</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
              <img
                src={result.final.url}
                alt="Background changed"
                className="w-full h-full object-contain"
              />
            </div>
            <div className="mt-3 text-sm text-gray-500">
              <p>{result.final.filename}</p>
              <p>High-quality PNG format</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Processing Steps Toggle */}
      <div className="flex justify-center">
        <Button
          variant="outline"
          onClick={() => setShowSteps(!showSteps)}
          className="flex items-center space-x-2"
        >
          {showSteps ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          <span>{showSteps ? 'Hide' : 'Show'} Processing Steps</span>
        </Button>
      </div>

      {/* Processing Steps */}
      {showSteps && (
        <Card>
          <CardHeader>
            <CardTitle>Processing Steps</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Step 1: Original */}
              <div className="text-center">
                <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden mb-3">
                  <img
                    src={result.original.url}
                    alt="Step 1: Original"
                    className="w-full h-full object-contain"
                  />
                </div>
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <span className="text-blue-600 font-bold text-sm">1</span>
                </div>
                <h3 className="font-semibold mb-1">Original Image</h3>
                <p className="text-gray-600 text-sm">Your uploaded image</p>
              </div>

              {/* Step 2: Background Removed */}
              <div className="text-center">
                <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden mb-3 relative">
                  {/* Checkerboard pattern for transparency */}
                  <div 
                    className="absolute inset-0 opacity-20"
                    style={{
                      backgroundImage: `
                        linear-gradient(45deg, #ccc 25%, transparent 25%), 
                        linear-gradient(-45deg, #ccc 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #ccc 75%), 
                        linear-gradient(-45deg, transparent 75%, #ccc 75%)
                      `,
                      backgroundSize: '20px 20px',
                      backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px'
                    }}
                  />
                  <img
                    src={result.removedBackground.url}
                    alt="Step 2: Background removed"
                    className="w-full h-full object-contain relative z-10"
                  />
                </div>
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <span className="text-blue-600 font-bold text-sm">2</span>
                </div>
                <h3 className="font-semibold mb-1">Background Removed</h3>
                <p className="text-gray-600 text-sm">AI removed the original background</p>
              </div>

              {/* Step 3: New Background */}
              <div className="text-center">
                <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden mb-3">
                  <img
                    src={result.final.url}
                    alt="Step 3: New background"
                    className="w-full h-full object-contain"
                  />
                </div>
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <span className="text-blue-600 font-bold text-sm">3</span>
                </div>
                <h3 className="font-semibold mb-1">New Background</h3>
                <p className="text-gray-600 text-sm">AI generated the new background</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        <Button
          onClick={handleDownload}
          disabled={isDownloading}
          className="flex items-center space-x-2"
        >
          <Download className="w-4 h-4" />
          <span>{isDownloading ? 'Downloading...' : 'Download Result'}</span>
        </Button>
        
        <Button
          variant="outline"
          onClick={handleShare}
          className="flex items-center space-x-2"
        >
          <Share2 className="w-4 h-4" />
          <span>Share</span>
        </Button>
        
        <Button
          variant="outline"
          onClick={onReset}
          className="flex items-center space-x-2"
        >
          <RotateCcw className="w-4 h-4" />
          <span>Change Another Image</span>
        </Button>
      </div>

      {/* Technical Details */}
      <Card className="bg-gray-50">
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
            <div>
              <p className="font-medium text-gray-700">Generation Method</p>
              <p className="text-gray-600">{getBackgroundTypeLabel(result.parameters.backgroundType)}</p>
            </div>
            <div>
              <p className="font-medium text-gray-700">Subject Detection</p>
              <p className="text-gray-600">{getSegmentTypeLabel(result.parameters.segmentType)}</p>
            </div>
            {result.parameters.prompt && (
              <div className="md:col-span-2">
                <p className="font-medium text-gray-700">Background Description</p>
                <p className="text-gray-600 text-xs">{result.parameters.prompt}</p>
              </div>
            )}
            <div>
              <p className="font-medium text-gray-700">Output Format</p>
              <p className="text-gray-600">High-quality PNG</p>
            </div>
            <div>
              <p className="font-medium text-gray-700">Request ID</p>
              <p className="text-gray-600 font-mono text-xs">{result.requestId}</p>
            </div>
            <div>
              <p className="font-medium text-gray-700">Status</p>
              <p className="text-green-600">✓ Completed</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
