/**
 * 图片处理工具函数
 * 用于处理图片与 base64 之间的转换，以及其他图片相关操作
 * 支持浏览器端和服务器端环境
 */

/**
 * 将 File 对象转换为 base64 字符串
 * @param file 图片文件
 * @returns Promise<string> base64 字符串（包含 data: 前缀）
 */
export async function fileToBase64(file: File): Promise<string> {
  try {
    // 检查是否在浏览器环境中
    if (typeof window !== 'undefined' && typeof FileReader !== 'undefined') {
      // 浏览器端使用 FileReader
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = error => reject(error);
      });
    } else {
      // 服务器端使用 arrayBuffer
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      const base64 = buffer.toString('base64');

      // 根据文件类型添加适当的 data URL 前缀
      const mimeType = file.type || 'image/png';
      return `data:${mimeType};base64,${base64}`;
    }
  } catch (error) {
    throw new Error(`Failed to convert file to base64: ${error}`);
  }
}

/**
 * 将 base64 字符串转换为 File 对象
 * @param base64 base64 字符串（可以包含或不包含 data: 前缀）
 * @param fileName 文件名
 * @param fileType 文件类型
 * @returns Promise<File>
 */
export async function base64ToFile(base64: string, fileName: string, fileType: string): Promise<File> {
  const response = await fetch(base64);
  const blob = await response.blob();
  return new File([blob], fileName, { type: fileType });
}

/**
 * 将 base64 字符串转换为 Buffer
 * @param base64 base64 字符串（可以包含或不包含 data: 前缀）
 * @returns Buffer
 */
export function base64ToBuffer(base64: string): Buffer {
  // 移除 data: 前缀（如果存在）
  const base64Data = base64.includes(',') ? base64.split(',')[1] : base64;
  return Buffer.from(base64Data, 'base64');
}

/**
 * 将 Buffer 转换为 base64 字符串
 * @param buffer Buffer 对象
 * @param mimeType MIME 类型（可选）
 * @returns base64 字符串（包含 data: 前缀）
 */
export function bufferToBase64(buffer: Buffer, mimeType?: string): string {
  const base64 = buffer.toString('base64');
  if (mimeType) {
    return `data:${mimeType};base64,${base64}`;
  }
  return base64;
}

/**
 * 从 URL 下载图片并转换为 base64
 * @param url 图片 URL
 * @returns Promise<string> base64 字符串（不包含 data: 前缀）
 */
export async function urlToBase64(url: string): Promise<string> {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    return buffer.toString('base64');
  } catch (error) {
    console.error('Failed to convert URL to base64:', error);
    throw error;
  }
}

/**
 * 清理 base64 字符串，移除 data: 前缀
 * @param base64 base64 字符串
 * @returns 纯 base64 字符串
 */
export function cleanBase64(base64: string): string {
  return base64.includes(',') ? base64.split(',')[1] : base64;
}

/**
 * 添加 data: 前缀到 base64 字符串
 * @param base64 纯 base64 字符串
 * @param mimeType MIME 类型
 * @returns 完整的 data URL
 */
export function addDataPrefix(base64: string, mimeType: string = 'image/png'): string {
  if (base64.startsWith('data:')) {
    return base64;
  }
  return `data:${mimeType};base64,${base64}`;
}

/**
 * 检测 base64 字符串的图片类型
 * @param base64 base64 字符串
 * @returns MIME 类型或 null
 */
export function detectImageType(base64: string): string | null {
  if (base64.startsWith('data:')) {
    const match = base64.match(/^data:([^;]+);base64,/);
    return match ? match[1] : null;
  }
  
  // 通过 base64 头部特征检测
  const cleanedBase64 = cleanBase64(base64);
  const header = cleanedBase64.substring(0, 20);
  
  // PNG: iVBORw0KGgo
  if (header.startsWith('iVBORw0KGgo')) {
    return 'image/png';
  }
  
  // JPEG: /9j/
  if (header.startsWith('/9j/')) {
    return 'image/jpeg';
  }
  
  // WebP: UklGR
  if (header.startsWith('UklGR')) {
    return 'image/webp';
  }
  
  // GIF: R0lGODlh or R0lGODdh
  if (header.startsWith('R0lGODlh') || header.startsWith('R0lGODdh')) {
    return 'image/gif';
  }
  
  return null;
}

/**
 * 验证 base64 字符串是否为有效的图片
 * @param base64 base64 字符串
 * @returns boolean
 */
export function isValidImageBase64(base64: string): boolean {
  try {
    const cleanedBase64 = cleanBase64(base64);
    
    // 检查是否为有效的 base64
    if (!/^[A-Za-z0-9+/]*={0,2}$/.test(cleanedBase64)) {
      return false;
    }
    
    // 检查是否能检测到图片类型
    return detectImageType(base64) !== null;
  } catch {
    return false;
  }
}

/**
 * 获取 base64 图片的大小（字节）
 * @param base64 base64 字符串
 * @returns 字节大小
 */
export function getBase64Size(base64: string): number {
  const cleanedBase64 = cleanBase64(base64);
  // base64 编码后的大小约为原始大小的 4/3
  return Math.floor((cleanedBase64.length * 3) / 4);
}

/**
 * 格式化文件大小显示
 * @param bytes 字节数
 * @returns 格式化的大小字符串
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
