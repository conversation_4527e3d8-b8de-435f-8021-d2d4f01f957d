/**
 * 背景修改模型提供商枚举
 */
export enum BackgroundProvider {
  ALICLOUD = 'alicloud',
  VOLCENGINE = 'volcengine'
}

/**
 * 背景修改配置接口
 */
export interface BackgroundConfig {
  provider: BackgroundProvider;
  alicloud: {
    enabled: boolean;
    segmentModel: 'common' | 'body' | 'hair';
    backgroundModel: 'wanx-background-generation-v2';
  };
  volcengine: {
    enabled: boolean;
    model: 'doubao-seededit-3-0-i2i-250628';
    defaultGuidanceScale: number;
    defaultWatermark: boolean;
  };
}

/**
 * 背景修改配置管理类
 * 遵循SRP原则：只负责背景修改相关的配置管理
 */
export class BackgroundConfigManager {
  private static instance: BackgroundConfigManager;
  private config: BackgroundConfig;

  private constructor() {
    // 从环境变量获取配置
    const configuredProvider = process.env.BACKGROUND_PROVIDER?.toLowerCase();
    
    // 验证配置的有效性
    if (configuredProvider === 'volcengine') {
      this.validateVolcEngineConfig();
      this.config = {
        provider: BackgroundProvider.VOLCENGINE,
        alicloud: {
          enabled: false,
          segmentModel: 'common',
          backgroundModel: 'wanx-background-generation-v2'
        },
        volcengine: {
          enabled: true,
          model: 'doubao-seededit-3-0-i2i-250628',
          defaultGuidanceScale: parseFloat(process.env.VOLCENGINE_GUIDANCE_SCALE || '5.5'),
          defaultWatermark: process.env.VOLCENGINE_WATERMARK === 'true' // 默认为false，只有明确设置为true才添加水印
        }
      };
    } else {
      // 默认使用阿里云
      this.validateAliCloudConfig();
      this.config = {
        provider: BackgroundProvider.ALICLOUD,
        alicloud: {
          enabled: true,
          segmentModel: (process.env.ALICLOUD_SEGMENT_MODEL as any) || 'common',
          backgroundModel: 'wanx-background-generation-v2'
        },
        volcengine: {
          enabled: false,
          model: 'doubao-seededit-3-0-i2i-250628',
          defaultGuidanceScale: 5.5,
          defaultWatermark: false // 默认不添加水印
        }
      };
    }
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): BackgroundConfigManager {
    if (!BackgroundConfigManager.instance) {
      BackgroundConfigManager.instance = new BackgroundConfigManager();
    }
    return BackgroundConfigManager.instance;
  }

  /**
   * 获取当前配置
   */
  public getConfig(): BackgroundConfig {
    return this.config;
  }

  /**
   * 获取当前提供商
   */
  public getProvider(): BackgroundProvider {
    return this.config.provider;
  }

  /**
   * 检查是否为阿里云
   */
  public isAliCloud(): boolean {
    return this.config.provider === BackgroundProvider.ALICLOUD;
  }

  /**
   * 检查是否为火山引擎
   */
  public isVolcEngine(): boolean {
    return this.config.provider === BackgroundProvider.VOLCENGINE;
  }

  /**
   * 获取阿里云配置
   */
  public getAliCloudConfig() {
    return this.config.alicloud;
  }

  /**
   * 获取火山引擎配置
   */
  public getVolcEngineConfig() {
    return this.config.volcengine;
  }

  /**
   * 验证阿里云配置
   */
  private validateAliCloudConfig(): void {
    if (!process.env.ALICLOUD_ACCESS_KEY_ID || !process.env.ALICLOUD_ACCESS_KEY_SECRET) {
      throw new Error('AliCloud configuration is incomplete. Please set ALICLOUD_ACCESS_KEY_ID and ALICLOUD_ACCESS_KEY_SECRET');
    }
    
    if (!process.env.DASHSCOPE_API_KEY) {
      throw new Error('DashScope configuration is incomplete. Please set DASHSCOPE_API_KEY');
    }
  }

  /**
   * 验证火山引擎配置
   */
  private validateVolcEngineConfig(): void {
    if (!process.env.VOLCENGINE_API_KEY && !process.env.ARK_API_KEY) {
      throw new Error('VolcEngine configuration is incomplete. Please set VOLCENGINE_API_KEY or ARK_API_KEY');
    }
  }

  /**
   * 获取提供商显示名称
   */
  public getProviderDisplayName(): string {
    switch (this.config.provider) {
      case BackgroundProvider.ALICLOUD:
        return 'AliCloud (阿里云)';
      case BackgroundProvider.VOLCENGINE:
        return 'VolcEngine (火山引擎)';
      default:
        return 'Unknown';
    }
  }

  /**
   * 获取配置摘要信息
   */
  public getConfigSummary() {
    return {
      provider: this.config.provider,
      providerName: this.getProviderDisplayName(),
      alicloudEnabled: this.config.alicloud.enabled,
      volcengineEnabled: this.config.volcengine.enabled,
      segmentModel: this.config.alicloud.segmentModel,
      volcengineModel: this.config.volcengine.model
    };
  }
}
