/**
 * 异步上传服务
 * 用于在后台异步上传图片到 OSS，减少用户等待时间
 */

import { newStorage } from './storage';
import { base64ToBuffer, detectImageType, formatFileSize } from './image-utils';

export interface AsyncUploadTask {
  id: string;
  base64Data: string;
  key: string;
  contentType?: string;
  disposition?: 'inline' | 'attachment';
  metadata?: Record<string, string>;
}

export interface AsyncUploadResult {
  taskId: string;
  success: boolean;
  url?: string;
  error?: string;
  uploadedAt?: Date;
}

/**
 * 异步上传管理器
 */
class AsyncUploadManager {
  private static instance: AsyncUploadManager;
  private uploadQueue: Map<string, AsyncUploadTask> = new Map();
  private results: Map<string, AsyncUploadResult> = new Map();
  private processing: Set<string> = new Set();

  private constructor() {}

  static getInstance(): AsyncUploadManager {
    if (!AsyncUploadManager.instance) {
      AsyncUploadManager.instance = new AsyncUploadManager();
    }
    return AsyncUploadManager.instance;
  }

  /**
   * 添加异步上传任务
   * @param task 上传任务
   * @returns 任务 ID
   */
  addUploadTask(task: AsyncUploadTask): string {
    this.uploadQueue.set(task.id, task);
    
    // 立即开始处理（不等待）
    this.processTask(task.id).catch(error => {
      console.error(`Async upload task ${task.id} failed:`, error);
      this.results.set(task.id, {
        taskId: task.id,
        success: false,
        error: error.message,
        uploadedAt: new Date()
      });
    });

    return task.id;
  }

  /**
   * 获取上传结果
   * @param taskId 任务 ID
   * @returns 上传结果或 null（如果还在处理中）
   */
  getUploadResult(taskId: string): AsyncUploadResult | null {
    return this.results.get(taskId) || null;
  }

  /**
   * 检查任务是否正在处理
   * @param taskId 任务 ID
   * @returns boolean
   */
  isProcessing(taskId: string): boolean {
    return this.processing.has(taskId);
  }

  /**
   * 处理上传任务
   * @param taskId 任务 ID
   */
  private async processTask(taskId: string): Promise<void> {
    if (this.processing.has(taskId)) {
      return; // 已在处理中
    }

    const task = this.uploadQueue.get(taskId);
    if (!task) {
      throw new Error(`Upload task ${taskId} not found`);
    }

    this.processing.add(taskId);

    try {
      console.log(`Starting async upload for task ${taskId}`);
      
      // 转换 base64 为 Buffer
      const buffer = base64ToBuffer(task.base64Data);
      
      // 检测内容类型（如果未提供）
      let contentType = task.contentType;
      if (!contentType) {
        const detectedType = detectImageType(task.base64Data);
        contentType = detectedType || 'image/png';
      }

      console.log(`Uploading ${formatFileSize(buffer.length)} to ${task.key} with type ${contentType}`);

      // 上传到存储
      const storage = newStorage();
      const uploadResult = await storage.uploadFile({
        body: buffer,
        key: task.key,
        contentType,
        disposition: task.disposition || 'inline'
      });

      // 保存成功结果
      this.results.set(taskId, {
        taskId,
        success: true,
        url: uploadResult.url,
        uploadedAt: new Date()
      });

      console.log(`Async upload completed for task ${taskId}: ${uploadResult.url}`);

    } catch (error: any) {
      console.error(`Async upload failed for task ${taskId}:`, error);
      
      // 保存失败结果
      this.results.set(taskId, {
        taskId,
        success: false,
        error: error.message,
        uploadedAt: new Date()
      });
    } finally {
      this.processing.delete(taskId);
      this.uploadQueue.delete(taskId); // 清理队列
    }
  }

  /**
   * 清理过期的结果（可选的清理机制）
   * @param maxAge 最大保留时间（毫秒）
   */
  cleanupResults(maxAge: number = 24 * 60 * 60 * 1000): void {
    const now = new Date();
    for (const [taskId, result] of this.results.entries()) {
      if (result.uploadedAt && (now.getTime() - result.uploadedAt.getTime()) > maxAge) {
        this.results.delete(taskId);
      }
    }
  }
}

/**
 * 创建异步上传任务
 * @param base64Data base64 图片数据
 * @param key 存储键
 * @param options 可选参数
 * @returns 任务 ID
 */
export function createAsyncUploadTask(
  base64Data: string,
  key: string,
  options?: {
    contentType?: string;
    disposition?: 'inline' | 'attachment';
    metadata?: Record<string, string>;
  }
): string {
  const taskId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  const task: AsyncUploadTask = {
    id: taskId,
    base64Data,
    key,
    contentType: options?.contentType,
    disposition: options?.disposition,
    metadata: options?.metadata
  };

  const manager = AsyncUploadManager.getInstance();
  return manager.addUploadTask(task);
}

/**
 * 获取异步上传结果
 * @param taskId 任务 ID
 * @returns 上传结果或 null
 */
export function getAsyncUploadResult(taskId: string): AsyncUploadResult | null {
  const manager = AsyncUploadManager.getInstance();
  return manager.getUploadResult(taskId);
}

/**
 * 检查异步上传是否正在处理
 * @param taskId 任务 ID
 * @returns boolean
 */
export function isAsyncUploadProcessing(taskId: string): boolean {
  const manager = AsyncUploadManager.getInstance();
  return manager.isProcessing(taskId);
}

/**
 * 立即上传 base64 图片（同步方式，用于对比）
 * @param base64Data base64 图片数据
 * @param key 存储键
 * @param options 可选参数
 * @returns 上传结果
 */
export async function uploadBase64Immediately(
  base64Data: string,
  key: string,
  options?: {
    contentType?: string;
    disposition?: 'inline' | 'attachment';
  }
): Promise<{ url: string; filename?: string; key: string }> {
  const buffer = base64ToBuffer(base64Data);

  let contentType = options?.contentType;
  if (!contentType) {
    const detectedType = detectImageType(base64Data);
    contentType = detectedType || 'image/png';
  }

  const storage = newStorage();
  const result = await storage.uploadFile({
    body: buffer,
    key,
    contentType,
    disposition: options?.disposition || 'inline'
  });

  // 确保返回的类型符合预期
  return {
    url: result.url || '',
    filename: result.filename,
    key: result.key || key
  };
}
