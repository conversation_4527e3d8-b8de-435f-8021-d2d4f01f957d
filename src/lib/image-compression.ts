/**
 * Image compression utility for reducing file size before upload
 */

interface CompressionOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  maxSizeMB?: number;
  preserveFormat?: boolean;
  minCompressionRatio?: number;
}

export async function compressImage(
  file: File,
  options: CompressionOptions = {}
): Promise<File> {
  const {
    maxWidth = 2048, // DashScope 要求长边不超过 2048 像素
    maxHeight = 2048, // DashScope 要求长边不超过 2048 像素
    quality = 0.95, // 提高质量到 95%
    maxSizeMB = 8,
    preserveFormat = true, // 默认保持原始格式
    minCompressionRatio = 0.1 // 最小压缩比例，避免过度压缩
  } = options;

  // If file is already small enough, return as is
  if (file.size <= maxSizeMB * 1024 * 1024) {
    return file;
  }

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      try {
        // Calculate new dimensions
        let { width, height } = img;
        
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width *= ratio;
          height *= ratio;
        }

        // Set canvas dimensions
        canvas.width = width;
        canvas.height = height;

        // Draw and compress with better quality settings
        if (ctx) {
          // 使用更好的图像渲染设置
          ctx.imageSmoothingEnabled = true;
          ctx.imageSmoothingQuality = 'high';
          ctx.drawImage(img, 0, 0, width, height);
        }

        // 确定输出格式和质量
        const originalType = file.type;
        let outputType = originalType;
        let outputQuality = quality;
        let fileExtension = file.name.split('.').pop()?.toLowerCase() || 'jpg';

        // 如果保持原始格式且是支持的格式
        if (preserveFormat && (originalType === 'image/png' || originalType === 'image/webp')) {
          outputType = originalType;
          // PNG 和 WebP 使用更高质量
          outputQuality = Math.min(0.95, quality + 0.1);
        } else {
          // 对于其他格式或不保持原始格式时，使用 JPEG
          outputType = 'image/jpeg';
          fileExtension = 'jpg';
        }

        // Convert to blob with compression
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Failed to compress image'));
              return;
            }

            // 检查压缩比例，如果压缩效果不好，尝试更激进的压缩
            const compressionRatio = blob.size / file.size;
            if (compressionRatio > (1 - minCompressionRatio) && blob.size > maxSizeMB * 1024 * 1024) {
              // 如果压缩效果不够好，使用更低的质量重新压缩
              canvas.toBlob(
                (secondBlob) => {
                  if (!secondBlob) {
                    reject(new Error('Failed to compress image on second attempt'));
                    return;
                  }

                  const finalFile = new File(
                    [secondBlob],
                    file.name.replace(/\.[^/.]+$/, `.${fileExtension}`),
                    {
                      type: outputType,
                      lastModified: Date.now(),
                    }
                  );

                  console.log(`Image compressed (2nd pass): ${file.size} bytes -> ${finalFile.size} bytes (${(finalFile.size/file.size*100).toFixed(1)}%)`);
                  resolve(finalFile);
                },
                outputType,
                Math.max(0.6, outputQuality - 0.2) // 降低质量进行二次压缩
              );
              return;
            }

            // Create new file with compressed data
            const compressedFile = new File(
              [blob],
              file.name.replace(/\.[^/.]+$/, `.${fileExtension}`),
              {
                type: outputType,
                lastModified: Date.now(),
              }
            );

            console.log(`Image compressed: ${file.size} bytes -> ${compressedFile.size} bytes (${(compressedFile.size/file.size*100).toFixed(1)}%)`);
            resolve(compressedFile);
          },
          outputType,
          outputQuality
        );
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    // Load the image
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Check if image needs compression
 */
export function shouldCompressImage(file: File, maxSizeMB: number = 6): boolean {
  return file.size > maxSizeMB * 1024 * 1024;
}

/**
 * Smart compression check - only compress if really necessary
 */
export function needsSmartCompression(file: File): boolean {
  const sizeMB = file.size / (1024 * 1024);

  // 只有当文件真的很大时才压缩
  if (sizeMB > 10) return true; // 超过 10MB 必须压缩
  if (sizeMB > 6 && file.type === 'image/jpeg') return true; // JPEG 超过 6MB 压缩
  if (sizeMB > 8 && file.type === 'image/png') return false; // PNG 8MB 以下不压缩（保持质量）
  if (sizeMB > 6) return true; // 其他格式超过 6MB 压缩

  return false;
}

/**
 * Get human readable file size
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Prepare image for DashScope API - ensures RGBA format, proper dimensions, and optimal file size
 */
export async function prepareDashScopeImage(file: File): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      try {
        let { width, height } = img;

        // DashScope requirement: longest side should not exceed 2048 pixels
        const maxDimension = 2048;
        if (width > maxDimension || height > maxDimension) {
          const ratio = Math.min(maxDimension / width, maxDimension / height);
          width = Math.floor(width * ratio);
          height = Math.floor(height * ratio);
        }

        // 进一步优化尺寸以减少文件大小和下载时间
        // 如果图片仍然很大，适当缩小以提高下载速度
        const targetMaxDimension = 1536; // 降低到 1536 以提高下载速度
        if (width > targetMaxDimension || height > targetMaxDimension) {
          const ratio = Math.min(targetMaxDimension / width, targetMaxDimension / height);
          width = Math.floor(width * ratio);
          height = Math.floor(height * ratio);
        }

        // Set canvas dimensions
        canvas.width = width;
        canvas.height = height;

        // Clear canvas with transparent background
        if (ctx) {
          ctx.clearRect(0, 0, width, height);

          // Enable high-quality rendering
          ctx.imageSmoothingEnabled = true;
          ctx.imageSmoothingQuality = 'high';

          // Draw image
          ctx.drawImage(img, 0, 0, width, height);
        }

        // 尝试不同的压缩质量以平衡质量和文件大小
        const tryCompress = (quality: number) => {
          canvas.toBlob(
            (blob) => {
              if (!blob) {
                if (quality > 0.5) {
                  // 如果高质量失败，尝试更低质量
                  tryCompress(quality - 0.2);
                  return;
                }
                reject(new Error('Failed to prepare image for DashScope'));
                return;
              }

              // 检查文件大小，如果太大则降低质量
              const maxFileSize = 3 * 1024 * 1024; // 3MB 限制以确保快速下载
              if (blob.size > maxFileSize && quality > 0.5) {
                console.log(`File too large (${formatFileSize(blob.size)}), reducing quality from ${quality} to ${quality - 0.2}`);
                tryCompress(quality - 0.2);
                return;
              }

              // Create new file with PNG format
              const preparedFile = new File(
                [blob],
                file.name.replace(/\.[^/.]+$/, '.png'), // Ensure PNG extension
                {
                  type: 'image/png', // Force PNG format for RGBA support
                  lastModified: Date.now(),
                }
              );

              console.log(`Image prepared for DashScope: ${formatFileSize(file.size)} -> ${formatFileSize(preparedFile.size)}, dimensions: ${width}x${height}, quality: ${quality}`);
              resolve(preparedFile);
            },
            'image/png', // Force PNG format to ensure RGBA support
            quality
          );
        };

        // 开始压缩，从高质量开始
        tryCompress(0.9);
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image for DashScope preparation'));
    };

    // Load the image
    img.src = URL.createObjectURL(file);
  });
}
