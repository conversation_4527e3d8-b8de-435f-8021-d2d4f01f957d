"use client";

import { useState, useEffect } from 'react';
import { useAppContext } from '@/contexts/app';

interface UserCredits {
  left_credits: number;
  is_pro?: boolean;
  is_recharged?: boolean;
}

export function useUserCredits() {
  const { user } = useAppContext();
  const [credits, setCredits] = useState<UserCredits | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCredits = async () => {
    if (!user) {
      setCredits(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/get-user-credits', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.code !== 0) {
        throw new Error(result.message || 'Failed to fetch credits');
      }

      setCredits(result.data);
    } catch (err) {
      console.error('Failed to fetch user credits:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch credits');
      setCredits(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCredits();
  }, [user]);

  const hasEnoughCredits = (requiredCredits: number): boolean => {
    return credits ? credits.left_credits >= requiredCredits : false;
  };

  const refreshCredits = () => {
    fetchCredits();
  };

  return {
    credits,
    loading,
    error,
    hasEnoughCredits,
    refreshCredits,
  };
}
