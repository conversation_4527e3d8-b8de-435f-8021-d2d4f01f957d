import { NextRequest } from 'next/server';
import { respData, respErr } from '@/lib/resp';
import { createVolcEngineClient } from '@/aisdk/volcengine';
import { newStorage } from '@/lib/storage';
import { getUuid } from '@/lib/hash';
import { getUserUuid } from '@/services/user';
import { CreditsTransType, CREDITS_COST, decreaseCredits } from '@/services/credit';
import { fileToBase64, addDataPrefix } from '@/lib/image-utils';
import { createAsyncUploadTask } from '@/lib/async-upload';

/**
 * 根据用户输入的提示词语言，构建带前缀的完整提示词
 * @param userPrompt 用户输入的提示词
 * @returns 带前缀的完整提示词
 */
function buildPromptWithPrefix(userPrompt: string): string {
  const trimmedPrompt = userPrompt.trim();

  // 检测是否包含中文字符
  const hasChinese = /[\u4e00-\u9fa5]/.test(trimmedPrompt);

  if (hasChinese) {
    // 中文前缀
    return `保持主体不变，将背景修改为：${trimmedPrompt}`;
  } else {
    // 英文前缀
    return `Change background, keep the main subject unchanged, change the background to: ${trimmedPrompt}`;
  }
}

export async function POST(req: NextRequest) {
  // 记录接口开始时间
  const apiStartTime = Date.now();
  
  try {
    console.log('Change background API called');
    console.log(`API processing started at: ${new Date(apiStartTime).toISOString()}`);

    // Check authentication - change-background requires login
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr('Authentication required. Please login to use change background feature.');
    }

    console.log('User authenticated:', user_uuid);

    // Check content length before parsing
    const contentLength = req.headers.get('content-length');
    console.log('Request content length:', contentLength);

    if (contentLength && parseInt(contentLength) > 50 * 1024 * 1024) { // 50MB limit
      return respErr('Request too large. Please use a smaller image file.');
    }

    const formData = await req.formData();
    const file = formData.get('file') as File;
    const prompt = formData.get('prompt') as string || '';

    console.log('File received:', file ? `${file.name} (${file.size} bytes)` : 'No file');

    // For change-background feature, hide options and use defaults as per requirements
    const backgroundType = 'text'; // Default to Text-Guided
    const segmentType = 'common'; // Default to General Removal
    const referenceImageUrl = ''; // Not used for text-guided

    if (!file) {
      return respErr('No file provided');
    }

    if (!prompt) {
      return respErr('Prompt is required for background generation');
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return respErr('Invalid file type. Only JPEG, PNG, and WebP are supported.');
    }

    // 验证文件大小 (最大 8MB) - 平衡质量和限制
    const maxSize = 8 * 1024 * 1024; // 8MB
    if (file.size > maxSize) {
      return respErr('File size too large. Maximum size is 8MB.');
    }

    // Deduct credits for change-background usage (2 credits per use)
    try {
      await decreaseCredits({
        user_uuid,
        trans_type: CreditsTransType.ChangeBackground,
        credits: CREDITS_COST.CHANGE_BACKGROUND,
      });
    } catch (error) {
      console.error('Credit deduction failed:', error);
      return respErr('Insufficient credits. Please purchase more credits to use this feature.');
    }

    // 直接使用原始文件，不进行预处理
    console.log('Using original image file without preprocessing');

    // 生成UUID用于文件命名
    const uuid = getUuid();

    // 使用火山引擎进行完整处理（背景移除 + 背景生成）
    console.log('Using VolcEngine for complete background processing...');

    // 构建带前缀的提示词（根据语言自动选择中英文前缀）
    const fullPrompt = buildPromptWithPrefix(prompt);
    console.log('VolcEngine full prompt:', fullPrompt);

    let removedBgResult: any;
    let generatedUrls: string[] = [];

    const volcEngineClient = createVolcEngineClient();

      try {
        // 记录火山引擎开始时间
        const volcStartTime = Date.now();
        console.log(`VolcEngine processing started at: ${new Date(volcStartTime).toISOString()}`);

        // 将上传的文件转换为 base64
        const fileBase64 = await fileToBase64(file);
        console.log('Converted file to base64 for VolcEngine API');

        // 火山引擎直接处理 base64 图片，一步完成背景替换，返回 base64 结果
        const base64Results = await volcEngineClient.generateBackgroundImage(
          fileBase64, // 使用 base64 编码的图片
          fullPrompt, // 使用带前缀的完整提示词
          {
            guidance_scale: 5.5, // 默认引导强度
            watermark: false // 不添加水印
          },
          'base64' // 请求返回 base64 格式
        );

        // 计算火山引擎处理时间
        const volcEndTime = Date.now();
        const volcProcessingTime = (volcEndTime - volcStartTime) / 1000; // 转换为秒
        console.log(`VolcEngine processing completed in ${volcProcessingTime.toFixed(2)} seconds`);
        console.log(`VolcEngine processing started: ${new Date(volcStartTime).toISOString()}, ended: ${new Date(volcEndTime).toISOString()}`);
        console.log(`VolcEngine generated ${base64Results.length} base64 images`);

        // 创建异步上传任务
        const finalKey = `change-background/final/${uuid}_final.png`;
        const uploadTaskId = createAsyncUploadTask(
          base64Results[0], // 使用第一个生成的图片
          finalKey,
          {
            contentType: 'image/png',
            disposition: 'inline'
          }
        );

        console.log(`Created async upload task: ${uploadTaskId}`);

        // 立即返回 base64 数据，不等待上传完成
        generatedUrls = [addDataPrefix(base64Results[0], 'image/png')];

        // 静默异步上传原始文件到OSS（不影响现有逻辑）
        try {
          const originalBase64 = (await fileToBase64(file)).replace(/^data:image\/[^;]+;base64,/, '');
          const originalOssKey = `change-background/original/${uuid}_${file.name}`;
          createAsyncUploadTask(originalBase64, originalOssKey, {
            contentType: file.type,
            disposition: 'inline'
          });
          console.log(`Silent async upload started for original file: ${originalOssKey}`);
        } catch (error) {
          console.error('Silent original file upload failed:', error);
          // 不影响主流程，继续执行
        }

        // 静默异步上传最终结果到OSS（不影响现有逻辑）
        try {
          const finalOssKey = `change-background/final/${uuid}_final.png`;
          createAsyncUploadTask(base64Results[0], finalOssKey, {
            contentType: 'image/png',
            disposition: 'inline'
          });
          console.log(`Silent async upload started for final result: ${finalOssKey}`);
        } catch (error) {
          console.error('Silent final result upload failed:', error);
          // 不影响主流程，继续执行
        }

        // 设置空的中间结果，因为火山引擎一步完成
        removedBgResult = {
          url: '', // 火山引擎不提供中间步骤
          filename: '',
          key: ''
        };

      } catch (error: any) {
        console.error('VolcEngine complete processing failed:', error);
        return respErr(`Background processing failed: ${error.message}`);
      }

    // 处理最终结果 - 直接使用 base64 数据作为结果
    const finalResult = {
      url: generatedUrls[0], // 这是带有 data: 前缀的 base64 URL
      filename: `${uuid}_final.png`,
      key: `change-background/final/${uuid}_final.png`,
      isBase64: true // 标记这是 base64 数据
    };

    // 计算总体耗时
    const apiEndTime = Date.now();
    const totalProcessingTime = (apiEndTime - apiStartTime) / 1000; // 转换为秒
    console.log(`Total API processing completed in ${totalProcessingTime.toFixed(2)} seconds`);
    console.log(`API processing started: ${new Date(apiStartTime).toISOString()}, ended: ${new Date(apiEndTime).toISOString()}`);

    // 为前端显示创建原始图片的 base64 URL
    const originalBase64 = await fileToBase64(file);

    return respData({
      original: {
        url: originalBase64, // 返回原始文件的 base64 URL 用于前端显示
        filename: file.name,
        size: file.size,
        type: file.type,
      },
      removedBackground: {
        url: removedBgResult.url,
        filename: removedBgResult.filename,
        key: removedBgResult.key,
      },
      final: {
        url: finalResult.url,
        filename: finalResult.filename,
        key: finalResult.key,
      },
      parameters: {
        backgroundType,
        prompt: fullPrompt,
        referenceImageUrl,
        segmentType,
        provider: 'volcengine',
        providerName: 'VolcEngine'
      },
      requestId: '', // 火山引擎不提供requestId
    });

  } catch (error: any) {
    // 计算错误情况下的总耗时
    const apiEndTime = Date.now();
    const totalProcessingTime = (apiEndTime - apiStartTime) / 1000;
    console.error(`Change background API error after ${totalProcessingTime.toFixed(2)} seconds:`, error);
    return respErr(`Background change failed: ${error.message}`);
  }
}

// API information - options are fixed for change-background feature
export async function GET() {
  return respData({
    backgroundType: 'text', // Fixed to Text-Guided as per requirements
    backgroundName: 'Text-Guided Background Generation',
    segmentationType: 'common', // Fixed to General Removal as per requirements
    segmentationName: 'General Background Removal',
    requiresAuthentication: true,
    creditCost: 2, // 换背景每次扣除2积分
    maxFileSize: '8MB',
    supportedFormats: ['JPEG', 'PNG', 'WebP'],
    provider: 'volcengine',
    providerName: 'VolcEngine',
    processingSteps: [
      'Upload and validate image',
      'Generate new background using VolcEngine AI',
      'Download final result with new background',
    ],
  });
}
