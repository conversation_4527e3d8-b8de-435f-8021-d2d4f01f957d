import { NextRequest } from 'next/server';
import { respData, respErr } from '@/lib/resp';
import { createOptimizedAliCloudClient, SegmentType, ReturnForm } from '@/aisdk/alicloud';
import { newStorage } from '@/lib/storage';
import { getUuid } from '@/lib/hash';
import { getUserUuid } from '@/services/user';
import { CreditsTransType, CREDITS_COST, decreaseCredits } from '@/services/credit';
import { createAsyncUploadTask } from '@/lib/async-upload';
import { fileToBase64, addDataPrefix } from '@/lib/image-utils';

export async function POST(req: NextRequest) {
  try {
    console.log('Remove background API called');

    // Check authentication - remove-background requires login
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr('Authentication required. Please login to use remove background feature.');
    }

    console.log('User authenticated:', user_uuid);

    // Check content length before parsing
    const contentLength = req.headers.get('content-length');
    console.log('Request content length:', contentLength);

    if (contentLength && parseInt(contentLength) > 50 * 1024 * 1024) { // 50MB limit
      return respErr('Request too large. Please use a smaller image file.');
    }

    const formData = await req.formData();
    const file = formData.get('file') as File;

    console.log('File received:', file ? `${file.name} (${file.size} bytes)` : 'No file');
    // For remove-background feature, always use 'common' (General Removal) as per requirements
    const segmentType = 'common';
    const returnForm = formData.get('returnForm') as 'whiteBK' | 'cutOut' || 'cutOut';

    if (!file) {
      return respErr('No file provided');
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return respErr('Invalid file type. Only JPEG, PNG, and WebP are supported.');
    }

    // 验证文件大小 (最大 8MB) - 平衡质量和限制
    const maxSize = 8 * 1024 * 1024; // 8MB
    if (file.size > maxSize) {
      return respErr('File size too large. Maximum size is 8MB.');
    }

    // Deduct credits for remove-background usage (1 credit per use)
    try {
      await decreaseCredits({
        user_uuid,
        trans_type: CreditsTransType.RemoveBackground,
        credits: CREDITS_COST.REMOVE_BACKGROUND,
      });
    } catch (error) {
      console.error('Credit deduction failed:', error);
      return respErr('Insufficient credits. Please purchase more credits to use this feature.');
    }

    console.log(`Processing background removal for file: ${file.name}, type: ${segmentType}, returnForm: ${returnForm}`);

    // 将文件转换为Buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // 1. 上传原始文件到OSS存储
    const storage = newStorage();
    const uuid = getUuid();
    const originalKey = `background-removal/original/${uuid}_${file.name}`;

    console.log(`Uploading original file to OSS with key: ${originalKey}`);

    const uploadResult = await storage.uploadFile({
      body: buffer,
      key: originalKey,
      contentType: file.type,
      disposition: 'inline',
    });

    if (!uploadResult.url) {
      return respErr('Failed to upload original file to OSS');
    }

    console.log(`Original file uploaded successfully: ${uploadResult.url}`);

    // 2. 调用阿里云图像分割API（使用优化的官方SDK）
    const aliCloudClient = createOptimizedAliCloudClient();

    console.log(`Calling AliCloud API for ${segmentType} segmentation...`);

    // 转换参数类型
    const segmentTypeEnum = segmentType as SegmentType;
    const returnFormEnum = returnForm as ReturnForm;

    const segmentResult = await aliCloudClient.segment(
      segmentTypeEnum,
      uploadResult.url,
      returnFormEnum
    );

    console.log(`AliCloud API response:`, {
      code: segmentResult.code,
      message: segmentResult.message,
      requestId: segmentResult.requestId,
      hasData: !!segmentResult.data?.imageURL
    });

    if (!segmentResult.data?.imageURL) {
      return respErr(`Background removal failed: ${segmentResult.message || 'No processed image URL returned'}`);
    }

    // 3. 下载处理后的图片并上传到我们的存储（保持原有逻辑）
    const processedKey = `background-removal/processed/${uuid}_processed.png`;

    console.log(`Downloading processed image and uploading to OSS with key: ${processedKey}`);

    const finalResult = await storage.downloadAndUpload({
      url: segmentResult.data.imageURL,
      key: processedKey,
      contentType: 'image/png',
      disposition: 'inline',
    });

    console.log(`Processed image uploaded successfully: ${finalResult.url}`);

    // 静默异步上传原始文件到OSS（额外备份，不影响现有逻辑）
    try {
      const originalBase64 = buffer.toString('base64');
      const originalOssKey = `background-removal/original/${uuid}_${file.name}`;
      createAsyncUploadTask(originalBase64, originalOssKey, {
        contentType: file.type,
        disposition: 'inline'
      });
      console.log(`Silent async upload started for original file: ${originalOssKey}`);
    } catch (error) {
      console.error('Silent original file upload failed:', error);
      // 不影响主流程，继续执行
    }

    // 静默异步上传处理结果到OSS（额外备份，不影响现有逻辑）
    try {
      const processedResponse = await fetch(segmentResult.data.imageURL);
      if (processedResponse.ok) {
        const processedArrayBuffer = await processedResponse.arrayBuffer();
        const processedBase64 = Buffer.from(processedArrayBuffer).toString('base64');
        const processedOssKey = `background-removal/processed/${uuid}_processed_backup.png`;
        createAsyncUploadTask(processedBase64, processedOssKey, {
          contentType: 'image/png',
          disposition: 'inline'
        });
        console.log(`Silent async upload started for processed result: ${processedOssKey}`);
      }
    } catch (error) {
      console.error('Silent processed result upload failed:', error);
      // 不影响主流程，继续执行
    }

    return respData({
      original: {
        url: uploadResult.url,
        filename: file.name,
        size: file.size,
        type: file.type,
        key: uploadResult.key,
      },
      processed: {
        url: finalResult.url,
        filename: finalResult.filename,
        key: finalResult.key,
      },
      segmentType,
      returnForm,
      requestId: segmentResult.requestId,
    });

  } catch (error: any) {
    console.error('Background removal API error:', error);
    return respErr(`Background removal failed: ${error.message}`);
  }
}

// API information - segmentation type is fixed to 'common' for remove-background feature
export async function GET() {
  return respData({
    segmentationType: 'common', // Fixed to General Removal as per requirements
    segmentationName: 'General Background Removal',
    requiresAuthentication: true, // 需要登录
    creditCost: 1, // 每次扣除1积分
    supportedReturnForms: [
      {
        type: 'cutOut',
        name: 'Transparent Background',
        description: 'Remove background completely (transparent PNG)',
      },
      {
        type: 'whiteBK',
        name: 'White Background',
        description: 'Replace background with white color',
      },
    ],
    maxFileSize: '10MB',
    supportedFormats: ['JPEG', 'PNG', 'WebP'],
    processingSteps: [
      'Authenticate user and check credits',
      'Upload image to OSS storage',
      'Call AliCloud image segmentation API',
      'Download processed result',
      'Save final image to storage',
      'Return download URLs'
    ],
  });
}
