import { NextRequest, NextResponse } from 'next/server';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const imageUrl = searchParams.get('url');
    const filename = searchParams.get('filename') || 'download.png';

    if (!imageUrl) {
      return NextResponse.json({ error: 'Image URL is required' }, { status: 400 });
    }

    let imageBuffer: ArrayBuffer;
    let contentType = 'image/png';

    // Check if it's a base64 data URL
    if (imageUrl.startsWith('data:')) {
      console.log('Processing base64 data URL for download');

      // Extract the base64 data and content type
      const [header, base64Data] = imageUrl.split(',');
      if (!base64Data) {
        return NextResponse.json({ error: 'Invalid base64 data URL' }, { status: 400 });
      }

      // Extract content type from header (e.g., "data:image/png;base64")
      const contentTypeMatch = header.match(/data:([^;]+)/);
      if (contentTypeMatch) {
        contentType = contentTypeMatch[1];
      }

      // Convert base64 to buffer
      try {
        const buffer = Buffer.from(base64Data, 'base64');
        imageBuffer = buffer.buffer.slice(buffer.byteOffset, buffer.byteOffset + buffer.byteLength);
        console.log(`Successfully processed base64 data, size: ${imageBuffer.byteLength} bytes`);
      } catch (error) {
        console.error('Failed to decode base64 data:', error);
        return NextResponse.json({ error: 'Invalid base64 data' }, { status: 400 });
      }
    } else {
      // Handle regular HTTP URLs (existing logic)
      // Validate that the URL is from our OSS domain
      const allowedDomains = [
        'change-background-shipany.oss-cn-shanghai.aliyuncs.com',
        // Add other allowed domains if needed
      ];

      const urlObj = new URL(imageUrl);
      if (!allowedDomains.includes(urlObj.hostname)) {
        return NextResponse.json({ error: 'Invalid image URL domain' }, { status: 403 });
      }

      console.log(`Downloading image from: ${imageUrl}`);

      // Fetch the image from OSS
      const response = await fetch(imageUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; ImageDownloader/1.0)',
        },
      });

      if (!response.ok) {
        console.error(`Failed to fetch image: ${response.status} ${response.statusText}`);
        return NextResponse.json(
          { error: `Failed to fetch image: ${response.status}` },
          { status: response.status }
        );
      }

      contentType = response.headers.get('content-type') || 'image/png';
      imageBuffer = await response.arrayBuffer();
      console.log(`Successfully downloaded image, size: ${imageBuffer.byteLength} bytes`);
    }

    // Return the image with appropriate headers for download
    return new NextResponse(imageBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': imageBuffer.byteLength.toString(),
        'Cache-Control': 'no-cache',
      },
    });
  } catch (error) {
    console.error('Download image error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
