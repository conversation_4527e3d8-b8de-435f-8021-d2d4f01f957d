"use client";

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
// import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'; // 暂时不需要
import FileUpload from '@/components/background-removal/file-upload';
import ResultDisplay from '@/components/background-removal/result-display';
import { Scissors, User, Sparkles, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useFileUploadContext } from '@/contexts/file-upload';

interface RemoveBackgroundResult {
  original: {
    url: string;
    filename: string;
    size: number;
    type: string;
  };
  processed: {
    url: string;
    filename: string;
    key: string;
  };
  segmentType: string;
  requestId: string;
}

export default function RemoveBackgroundPage() {
  const { clearRemoveBackgroundState } = useFileUploadContext();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [result, setResult] = useState<RemoveBackgroundResult | null>(null);
  const segmentType = 'common'; // 固定使用common类型
  const returnForm = 'cutOut'; // 固定使用透明背景，不让用户选择

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
    setResult(null); // Reset previous results
  };

  const handleUploadComplete = (uploadResult: RemoveBackgroundResult) => {
    setResult(uploadResult);
  };

  const handleReset = () => {
    setSelectedFile(null);
    setResult(null);
    clearRemoveBackgroundState(); // Clear context state
  };

  // segmentTypes 定义已移除，因为我们使用固定值

  if (result) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          <ResultDisplay result={result} onReset={handleReset} />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Link href="/" className="mr-4">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Button>
            </Link>
          </div>
          
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            AI Background Removal
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Remove backgrounds from your images instantly with our advanced AI technology. 
            Perfect for e-commerce, social media, and professional photography.
          </p>
        </div>

        {/* Features - Hidden segment type selection, using General Removal by default */}
        {/*
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {segmentTypes.map((type) => {
            const Icon = type.icon;
            return (
              <Card
                key={type.id}
                className={`cursor-pointer transition-all hover:shadow-lg ${
                  segmentType === type.id
                    ? 'ring-2 ring-blue-500 bg-blue-50'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => setSegmentType(type.id)}
              >
                <CardHeader className="text-center">
                  <div className="flex items-center justify-center mb-2">
                    <Icon className="w-8 h-8 text-blue-600" />
                    {type.recommended && (
                      <Badge className="ml-2" variant="secondary">
                        Recommended
                      </Badge>
                    )}
                  </div>
                  <CardTitle className="text-lg">{type.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 text-center text-sm">
                    {type.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>
        */}

        {/* Background Type Info - Fixed to Transparent */}

        {/* Upload Section */}
        <div className="mb-8">
          <FileUpload
            onFileSelect={handleFileSelect}
            onUploadComplete={handleUploadComplete}
            segmentType={segmentType}
            returnForm={returnForm}
          />
        </div>

        {/* Info Section */}
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>How it works</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-blue-600 font-bold">1</span>
                  </div>
                  <h3 className="font-semibold mb-2">Upload Image</h3>
                  <p className="text-gray-600 text-sm">
                    Choose your image and select the processing type that best fits your needs.
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-blue-600 font-bold">2</span>
                  </div>
                  <h3 className="font-semibold mb-2">AI Processing</h3>
                  <p className="text-gray-600 text-sm">
                    Our advanced AI analyzes your image and removes the background with precision.
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-blue-600 font-bold">3</span>
                  </div>
                  <h3 className="font-semibold mb-2">Download Result</h3>
                  <p className="text-gray-600 text-sm">
                    Get your image with transparent background in high quality PNG format.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Technical Specs */}
        <div className="max-w-2xl mx-auto mt-8">
          <Card className="bg-gray-100">
            <CardContent className="pt-6">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="font-medium text-gray-700">Supported Formats</p>
                  <p className="text-gray-600">JPEG, PNG, WebP</p>
                </div>
                <div>
                  <p className="font-medium text-gray-700">Max File Size</p>
                  <p className="text-gray-600">10 MB</p>
                </div>
                <div>
                  <p className="font-medium text-gray-700">Output Format</p>
                  <p className="text-gray-600">PNG with transparency</p>
                </div>
                <div>
                  <p className="font-medium text-gray-700">Processing Time</p>
                  <p className="text-gray-600">2-10 seconds</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
