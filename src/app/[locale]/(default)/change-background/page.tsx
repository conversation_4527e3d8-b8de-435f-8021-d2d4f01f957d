"use client";

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import BackgroundUpload from '@/components/change-background/background-upload';
import BackgroundResult from '@/components/change-background/background-result';
import { ArrowLeft, Wand2, Image, Type, Sparkles } from 'lucide-react';
import Link from 'next/link';
import { useFileUploadContext } from '@/contexts/file-upload';

interface BackgroundChangeResult {
  original: {
    url: string;
    filename: string;
    size: number;
    type: string;
  };
  removedBackground: {
    url: string;
    filename: string;
    key: string;
  };
  final: {
    url: string;
    filename: string;
    key: string;
  };
  parameters: {
    backgroundType: string;
    prompt?: string;
    referenceImageUrl?: string;
    segmentType: string;
  };
  requestId: string;
}

export default function ChangeBackgroundPage() {
  const { clearChangeBackgroundState } = useFileUploadContext();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [result, setResult] = useState<BackgroundChangeResult | null>(null);

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
    setResult(null); // Reset previous results
  };

  const handleUploadComplete = (uploadResult: BackgroundChangeResult) => {
    setResult(uploadResult);
  };

  const handleReset = () => {
    setSelectedFile(null);
    setResult(null);
    clearChangeBackgroundState(); // Clear context state
  };

  const features = [
    {
      icon: Wand2,
      title: 'AI-Powered Processing',
      description: 'Advanced AI removes backgrounds and generates new ones with professional quality.',
    },
    {
      icon: Type,
      title: 'Text-Guided Generation',
      description: 'Describe your ideal background and watch AI bring it to life.',
    },
    {
      icon: Image,
      title: 'Image-Guided Styling',
      description: 'Use reference images to match specific styles and aesthetics.',
    },
    {
      icon: Sparkles,
      title: 'Combined Approach',
      description: 'Combine text descriptions with reference images for perfect results.',
    },
  ];

  if (result) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          <BackgroundResult result={result} onReset={handleReset} />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Link href="/" className="mr-4">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Button>
            </Link>
          </div>
          
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            AI Background Changer
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Transform your images with AI-powered background removal and generation. 
            Create stunning visuals by replacing backgrounds with custom scenes, 
            styles, or environments in seconds.
          </p>
        </div>

        {/* Upload Section */}
        <div className="mb-8">
          <BackgroundUpload
            onFileSelect={handleFileSelect}
            onUploadComplete={handleUploadComplete}
          />
        </div>

        {/* Features */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
                <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex justify-center mb-2">
                      <Icon className="w-8 h-8 text-blue-600" />
                    </div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 text-sm">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
            );
          })}
        </div>
        {/* How it works */}
        <div className="max-w-4xl mx-auto mb-8">
          <Card>
            <CardHeader>
              <CardTitle>How AI Background Change Works</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="font-semibold mb-3">Processing Steps:</h3>
                  <ol className="space-y-2 text-sm text-gray-600">
                    <li className="flex items-start">
                      <span className="bg-blue-100 text-blue-600 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">1</span>
                      <span>Upload your image and choose generation method</span>
                    </li>
                    <li className="flex items-start">
                      <span className="bg-blue-100 text-blue-600 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">2</span>
                      <span>AI removes the existing background with precision</span>
                    </li>
                    <li className="flex items-start">
                      <span className="bg-blue-100 text-blue-600 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">3</span>
                      <span>Generate new background based on your preferences</span>
                    </li>
                    <li className="flex items-start">
                      <span className="bg-blue-100 text-blue-600 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">4</span>
                      <span>Seamlessly combine subject with new background</span>
                    </li>
                    <li className="flex items-start">
                      <span className="bg-blue-100 text-blue-600 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">5</span>
                      <span>Download your professional-quality result</span>
                    </li>
                  </ol>
                </div>
                
                <div>
                  <h3 className="font-semibold mb-3">Generation Methods:</h3>
                  <div className="space-y-3 text-sm">
                    <div className="flex items-start">
                      <Type className="w-4 h-4 text-blue-600 mr-2 mt-0.5" />
                      <div>
                        <p className="font-medium">Text-Guided</p>
                        <p className="text-gray-600">Describe your ideal background in words</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <Image className="w-4 h-4 text-blue-600 mr-2 mt-0.5" />
                      <div>
                        <p className="font-medium">Image-Guided</p>
                        <p className="text-gray-600">Use a reference image for style matching</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <Sparkles className="w-4 h-4 text-blue-600 mr-2 mt-0.5" />
                      <div>
                        <p className="font-medium">Combined Approach</p>
                        <p className="text-gray-600">Mix text descriptions with reference images</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Technical Specs */}
        <div className="max-w-2xl mx-auto">
          <Card className="bg-gray-100">
            <CardContent className="pt-6">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="font-medium text-gray-700">Supported Formats</p>
                  <p className="text-gray-600">JPEG, PNG, WebP</p>
                </div>
                <div>
                  <p className="font-medium text-gray-700">Max File Size</p>
                  <p className="text-gray-600">10 MB</p>
                </div>
                <div>
                  <p className="font-medium text-gray-700">Output Quality</p>
                  <p className="text-gray-600">High-resolution PNG</p>
                </div>
                <div>
                  <p className="font-medium text-gray-700">Processing Time</p>
                  <p className="text-gray-600">30-60 seconds</p>
                </div>
                <div>
                  <p className="font-medium text-gray-700">AI Models</p>
                  <p className="text-gray-600">AliCloud + DashScope</p>
                </div>
                <div>
                  <p className="font-medium text-gray-700">Background Types</p>
                  <p className="text-gray-600">Text, Image, Combined</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
