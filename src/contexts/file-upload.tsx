"use client";

import {
  ReactNode,
  createContext,
  useContext,
  useState,
  useEffect,
} from "react";

interface FileUploadState {
  selectedFile: File | null;
  previewUrl: string | null;
  prompt?: string;
  returnForm?: 'cutOut' | 'whiteBK';
}

interface FileUploadContextValue {
  // Change background state
  changeBackgroundState: FileUploadState;
  setChangeBackgroundState: (state: FileUploadState) => void;
  
  // Remove background state
  removeBackgroundState: FileUploadState;
  setRemoveBackgroundState: (state: FileUploadState) => void;
  
  // Clear states
  clearChangeBackgroundState: () => void;
  clearRemoveBackgroundState: () => void;
  clearAllStates: () => void;
}

const FileUploadContext = createContext({} as FileUploadContextValue);

export const useFileUploadContext = () => useContext(FileUploadContext);

const initialState: FileUploadState = {
  selectedFile: null,
  previewUrl: null,
  prompt: '',
  returnForm: 'cutOut',
};

// Helper functions for localStorage and file persistence
const saveStateToStorage = async (key: string, state: FileUploadState) => {
  try {
    if (typeof window !== 'undefined') {
      // Only save file if it's not too large (limit to 10MB to avoid localStorage issues)
      let fileData = null;
      if (state.selectedFile && state.selectedFile.size <= 10 * 1024 * 1024) {
        fileData = await fileToBase64(state.selectedFile);
      }

      const persistentState = {
        prompt: state.prompt,
        returnForm: state.returnForm,
        hasFile: !!state.selectedFile,
        fileName: state.selectedFile?.name,
        fileSize: state.selectedFile?.size,
        fileType: state.selectedFile?.type,
        fileData: fileData,
        timestamp: Date.now(), // Add timestamp for cleanup
      };
      localStorage.setItem(key, JSON.stringify(persistentState));
    }
  } catch (error) {
    console.warn('Failed to save state to localStorage:', error);
    // If localStorage is full, try to clear old data and retry
    if (error instanceof Error && error.name === 'QuotaExceededError') {
      clearExpiredStates();
      try {
        localStorage.setItem(key, JSON.stringify({
          prompt: state.prompt,
          returnForm: state.returnForm,
          hasFile: false, // Don't save file if quota exceeded
          timestamp: Date.now(),
        }));
      } catch (retryError) {
        console.warn('Failed to save even minimal state:', retryError);
      }
    }
  }
};

const loadStateFromStorage = async (key: string) => {
  try {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem(key);
      if (saved) {
        const parsedState = JSON.parse(saved);
        
        // Reconstruct file from base64 if it exists
        if (parsedState.fileData && parsedState.fileName && parsedState.fileType) {
          const file = await base64ToFile(parsedState.fileData, parsedState.fileName, parsedState.fileType);
          return {
            ...parsedState,
            selectedFile: file,
          };
        }
        
        return parsedState;
      }
    }
  } catch (error) {
    console.warn('Failed to load state from localStorage:', error);
  }
  return null;
};

const clearStateFromStorage = (key: string) => {
  try {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(key);
    }
  } catch (error) {
    console.warn('Failed to clear state from localStorage:', error);
  }
};

// Utility functions for file conversion
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
};

const base64ToFile = async (base64: string, fileName: string, fileType: string): Promise<File> => {
  const response = await fetch(base64);
  const blob = await response.blob();
  return new File([blob], fileName, { type: fileType });
};

// Clean up expired states (older than 1 hour)
const clearExpiredStates = () => {
  try {
    if (typeof window !== 'undefined') {
      const keys = ['changeBackgroundState', 'removeBackgroundState'];
      const oneHourAgo = Date.now() - (60 * 60 * 1000);
      
      keys.forEach(key => {
        const saved = localStorage.getItem(key);
        if (saved) {
          try {
            const parsedState = JSON.parse(saved);
            if (parsedState.timestamp && parsedState.timestamp < oneHourAgo) {
              localStorage.removeItem(key);
            }
          } catch (error) {
            // If parsing fails, remove the corrupted data
            localStorage.removeItem(key);
          }
        }
      });
    }
  } catch (error) {
    console.warn('Failed to clear expired states:', error);
  }
};

export const FileUploadContextProvider = ({ children }: { children: ReactNode }) => {
  const [changeBackgroundState, setChangeBackgroundState] = useState<FileUploadState>(initialState);
  const [removeBackgroundState, setRemoveBackgroundState] = useState<FileUploadState>(initialState);

  // Load persisted states on mount
  useEffect(() => {
    const loadPersistedStates = async () => {
      try {
        const savedChangeState = await loadStateFromStorage('changeBackgroundState');
        const savedRemoveState = await loadStateFromStorage('removeBackgroundState');

        if (savedChangeState && savedChangeState.hasFile) {
          // Create new preview URL if file was restored
          let previewUrl = savedChangeState.previewUrl;
          if (savedChangeState.selectedFile && !previewUrl) {
            previewUrl = URL.createObjectURL(savedChangeState.selectedFile);
          }

          setChangeBackgroundState({
            selectedFile: savedChangeState.selectedFile || null,
            previewUrl: previewUrl,
            prompt: savedChangeState.prompt || '',
            returnForm: savedChangeState.returnForm || 'cutOut',
          });
        }

        if (savedRemoveState && savedRemoveState.hasFile) {
          // Create new preview URL if file was restored
          let previewUrl = savedRemoveState.previewUrl;
          if (savedRemoveState.selectedFile && !previewUrl) {
            previewUrl = URL.createObjectURL(savedRemoveState.selectedFile);
          }

          setRemoveBackgroundState({
            selectedFile: savedRemoveState.selectedFile || null,
            previewUrl: previewUrl,
            prompt: savedRemoveState.prompt || '',
            returnForm: savedRemoveState.returnForm || 'cutOut',
          });
        }
      } catch (error) {
        console.warn('Failed to load persisted states:', error);
      }
    };

    loadPersistedStates();
  }, []);

  // Enhanced setters that persist to localStorage
  const setChangeBackgroundStateWithPersistence = (state: FileUploadState) => {
    setChangeBackgroundState(state);
    saveStateToStorage('changeBackgroundState', state);
  };

  const setRemoveBackgroundStateWithPersistence = (state: FileUploadState) => {
    setRemoveBackgroundState(state);
    saveStateToStorage('removeBackgroundState', state);
  };

  const clearChangeBackgroundState = () => {
    // Clean up preview URL to prevent memory leaks
    if (changeBackgroundState.previewUrl) {
      URL.revokeObjectURL(changeBackgroundState.previewUrl);
    }
    setChangeBackgroundState(initialState);
    clearStateFromStorage('changeBackgroundState');
  };

  const clearRemoveBackgroundState = () => {
    // Clean up preview URL to prevent memory leaks
    if (removeBackgroundState.previewUrl) {
      URL.revokeObjectURL(removeBackgroundState.previewUrl);
    }
    setRemoveBackgroundState(initialState);
    clearStateFromStorage('removeBackgroundState');
  };

  const clearAllStates = () => {
    clearChangeBackgroundState();
    clearRemoveBackgroundState();
  };

  return (
    <FileUploadContext.Provider
      value={{
        changeBackgroundState,
        setChangeBackgroundState: setChangeBackgroundStateWithPersistence,
        removeBackgroundState,
        setRemoveBackgroundState: setRemoveBackgroundStateWithPersistence,
        clearChangeBackgroundState,
        clearRemoveBackgroundState,
        clearAllStates,
      }}
    >
      {children}
    </FileUploadContext.Provider>
  );
};
