export interface VolcEngineConfig {
  apiKey: string;
  baseUrl?: string;
}

export interface ImageEditRequest {
  model: string;
  prompt: string;
  image: string;
  response_format?: 'url' | 'b64_json';
  size?: 'adaptive' | '1024x1024' | '512x512';
  seed?: number;
  guidance_scale?: number;
  watermark?: boolean;
}

export interface ImageEditResponse {
  model: string;
  created: number;
  data: Array<{
    url?: string;
    b64_json?: string;
  }>;
  usage: {
    generated_images: number;
    output_tokens: number;
    total_tokens: number;
  };
}

export interface VolcEngineError {
  error: {
    message: string;
    type: string;
    code?: string;
  };
}

export class VolcEngineClient {
  private apiKey: string;
  private baseUrl: string;

  constructor(config: VolcEngineConfig) {
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl || 'https://ark.cn-beijing.volces.com';
  }

  /**
   * 图片编辑 - 背景修改
   */
  async editImage(request: ImageEditRequest): Promise<ImageEditResponse> {
    try {
      console.log('VolcEngine editImage request:', {
        model: request.model,
        prompt: request.prompt,
        has_image: !!request.image,
        response_format: request.response_format,
        size: request.size,
        seed: request.seed,
        guidance_scale: request.guidance_scale,
        watermark: request.watermark
      });

      const response = await fetch(`${this.baseUrl}/api/v3/images/generations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: request.model,
          prompt: request.prompt,
          image: request.image,
          response_format: request.response_format || 'url',
          size: request.size || 'adaptive',
          seed: request.seed,
          guidance_scale: request.guidance_scale || 5.5,
          watermark: request.watermark === true, // 默认为false，只有明确设置为true才添加水印
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`VolcEngine editImage HTTP error:`, {
          status: response.status,
          statusText: response.statusText,
          errorText
        });
        
        let errorData: VolcEngineError;
        try {
          errorData = JSON.parse(errorText);
        } catch {
          errorData = {
            error: {
              message: errorText || `HTTP ${response.status}: ${response.statusText}`,
              type: 'http_error',
              code: response.status.toString()
            }
          };
        }
        
        throw new Error(`VolcEngine API error: ${errorData.error.message}`);
      }

      const result: ImageEditResponse = await response.json();
      
      console.log('VolcEngine editImage response:', {
        model: result.model,
        created: result.created,
        data_count: result.data?.length || 0,
        usage: result.usage
      });

      if (!result.data || result.data.length === 0) {
        throw new Error('VolcEngine API returned no image data');
      }

      return result;
    } catch (error: any) {
      console.error('VolcEngine editImage failed:', error);
      throw error;
    }
  }

  /**
   * 生成带背景的图片（用于背景修改功能）
   * @param baseImageInput 可以是 URL 或 base64 编码的图片
   * @param prompt 提示词
   * @param options 可选参数
   * @param returnFormat 返回格式：'url' 或 'base64'
   */
  async generateBackgroundImage(
    baseImageInput: string,
    prompt: string,
    options?: {
      seed?: number;
      guidance_scale?: number;
      watermark?: boolean;
    },
    returnFormat: 'url' | 'base64' = 'base64'
  ): Promise<string[]> {
    try {
      const request: ImageEditRequest = {
        model: 'doubao-seededit-3-0-i2i-250628',
        prompt,
        image: baseImageInput, // 现在可以接受 URL 或 base64
        response_format: returnFormat === 'base64' ? 'b64_json' : 'url',
        size: 'adaptive',
        seed: options?.seed,
        guidance_scale: options?.guidance_scale || 5.5,
        watermark: options?.watermark === true, // 默认为false，只有明确设置为true才添加水印
      };

      const result = await this.editImage(request);

      // 根据返回格式提取数据
      let results: string[];
      if (returnFormat === 'base64') {
        results = result.data.map(item => {
          if (!item.b64_json) {
            throw new Error('Expected base64 data but received none');
          }
          return item.b64_json;
        });
        console.log(`VolcEngine background generation successful, generated ${results.length} base64 images`);
      } else {
        results = result.data.map(item => {
          if (!item.url) {
            throw new Error('Expected URL data but received none');
          }
          return item.url;
        });
        console.log(`VolcEngine background generation successful, generated ${results.length} image URLs`);
      }

      return results;
    } catch (error: any) {
      console.error('VolcEngine background generation failed:', error);
      throw error;
    }
  }
}

export function createVolcEngineClient(config?: VolcEngineConfig): VolcEngineClient {
  const defaultConfig: VolcEngineConfig = {
    apiKey: process.env.VOLCENGINE_API_KEY || process.env.ARK_API_KEY || '',
    baseUrl: process.env.VOLCENGINE_BASE_URL || 'https://ark.cn-beijing.volces.com',
  };

  if (!defaultConfig.apiKey) {
    throw new Error('VolcEngine API key not found. Please set VOLCENGINE_API_KEY or ARK_API_KEY environment variable.');
  }

  return new VolcEngineClient(config || defaultConfig);
}
