const ImagesegClient = require('@alicloud/imageseg20191230');
const ViapiClient = require('@alicloud/viapi20230117');
const OpenapiClient = require('@alicloud/openapi-client');
const TeaUtil = require('@alicloud/tea-util');
import { Readable } from 'stream';
import * as http from 'http';
import * as https from 'https';

/**
 * AliCloud 图像分割配置接口
 */
export interface AliCloudImageSegConfig {
  accessKeyId: string;
  accessKeySecret: string;
  endpoint?: string;
  regionId?: string;
}

/**
 * 分割响应接口
 */
export interface SegmentResponse {
  code: string;
  message: string;
  requestId: string;
  data?: {
    imageURL: string;
  };
}

/**
 * 分割类型枚举
 */
export enum SegmentType {
  COMMON = 'common',
  BODY = 'body', 
  HAIR = 'hair'
}

/**
 * 返回形式枚举
 */
export enum ReturnForm {
  CUT_OUT = 'cutOut',      // 透明背景
  WHITE_BK = 'whiteBK'     // 白色背景
}

/**
 * 支持的输入类型
 */
export type ImageInput = string | Buffer | Readable;

/**
 * 优化的阿里云图像分割客户端
 * 基于官方SDK，提供更稳定和完整的功能
 */
export class OptimizedAliCloudImageSegClient {
  private client: any; // 使用any类型避免CommonJS/ES6混合导致的类型问题
  private viapiClient: any; // viapi客户端用于异步结果查询
  private config: AliCloudImageSegConfig;

  constructor(config: AliCloudImageSegConfig) {
    this.config = {
      endpoint: 'imageseg.cn-shanghai.aliyuncs.com',
      regionId: 'cn-shanghai',
      ...config
    };

    // 初始化官方SDK客户端（使用正确的方式）
    const openApiConfig = new OpenapiClient.Config({
      accessKeyId: this.config.accessKeyId,
      accessKeySecret: this.config.accessKeySecret
    });
    // 设置访问域名
    openApiConfig.endpoint = this.config.endpoint;

    this.client = new ImagesegClient.default(openApiConfig);

    // 初始化viapi客户端用于异步结果查询
    const viapiConfig = new OpenapiClient.Config({
      accessKeyId: this.config.accessKeyId,
      accessKeySecret: this.config.accessKeySecret
    });
    viapiConfig.endpoint = 'viapi.cn-shanghai.aliyuncs.com';
    this.viapiClient = new ViapiClient.default(viapiConfig);
  }

  /**
   * 通用高清图像分割
   * @param imageInput 图像输入（URL、Buffer或Stream）
   * @param returnForm 返回形式
   * @returns 分割结果
   */
  async segmentCommon(
    imageInput: ImageInput,
    returnForm: ReturnForm = ReturnForm.CUT_OUT
  ): Promise<SegmentResponse> {
    try {
      if (typeof imageInput === 'string') {
        // URL输入 - 使用高清分割标准请求
        const request = new ImagesegClient.SegmentHDCommonImageRequest({
          imageUrl: imageInput,  // 注意：高清分割使用 imageUrl 而不是 imageURL
          returnForm: returnForm
        });
        const runtime = new TeaUtil.RuntimeOptions({});
        const response = await this.client.segmentHDCommonImageWithOptions(request, runtime);

        // 调试：打印响应结构
        console.log('HD segmentation API response:', JSON.stringify(response, null, 2));

        // 检查是否直接返回结果（同步模式）
        if (response.body?.data?.imageURL) {
          return this.formatResponse(response);
        }

        // 检查是否返回jobId（异步模式）
        let jobId: string | undefined;

        // 首先检查 requestId（阿里云高清分割API使用 requestId 作为 jobId）
        if (response.body?.requestId) {
          jobId = response.body.requestId;
        } else if (response.body?.RequestId) {
          jobId = response.body.RequestId;
        } else if (response.body?.data?.jobId) {
          jobId = response.body.data.jobId;
        } else if (response.body?.data?.JobId) {
          jobId = response.body.data.JobId;
        } else if (response.body?.jobId) {
          jobId = response.body.jobId;
        } else if (response.body?.JobId) {
          jobId = response.body.JobId;
        }

        if (jobId) {
          // 异步模式：轮询获取结果
          console.log(`Using jobId: ${jobId} for polling async result`);
          return await this.pollAsyncResult(jobId);
        }

        // 如果既没有直接结果也没有jobId，则报错
        console.error('HD segmentation API response structure:', response.body);
        throw new Error(`Unexpected response format from HD segmentation API. Response: ${JSON.stringify(response.body)}`);

      } else {
        // Buffer或Stream输入 - 使用高清分割Advance请求
        const request = new ImagesegClient.SegmentHDCommonImageAdvanceRequest();
        request.imageUrlObject = this.convertToStreamOrResponse(imageInput);  // 注意：高清分割使用 imageUrlObject
        request.returnForm = returnForm;

        const runtime = new TeaUtil.RuntimeOptions({});
        const response = await this.client.segmentHDCommonImageAdvance(request, runtime);

        // 调试：打印响应结构
        console.log('HD segmentation Advance API response:', JSON.stringify(response, null, 2));

        // 检查是否直接返回结果（同步模式）
        if (response.body?.data?.imageURL) {
          return this.formatResponse(response);
        }

        // 检查是否返回jobId（异步模式）
        let jobId: string | undefined;

        // 首先检查 requestId（阿里云高清分割API使用 requestId 作为 jobId）
        if (response.body?.requestId) {
          jobId = response.body.requestId;
        } else if (response.body?.RequestId) {
          jobId = response.body.RequestId;
        } else if (response.body?.data?.jobId) {
          jobId = response.body.data.jobId;
        } else if (response.body?.data?.JobId) {
          jobId = response.body.data.JobId;
        } else if (response.body?.jobId) {
          jobId = response.body.jobId;
        } else if (response.body?.JobId) {
          jobId = response.body.JobId;
        }

        if (jobId) {
          // 异步模式：轮询获取结果
          console.log(`Using jobId: ${jobId} for polling async result (Advance)`);
          return await this.pollAsyncResult(jobId);
        }

        // 如果既没有直接结果也没有jobId，则报错
        console.error('HD segmentation Advance API response structure:', response.body);
        throw new Error(`Unexpected response format from HD segmentation Advance API. Response: ${JSON.stringify(response.body)}`);
      }
    } catch (error: any) {
      throw new Error(`HD Common segmentation failed: ${error.message}`);
    }
  }

  /**
   * 人体分割
   * @param imageInput 图像输入
   * @param returnForm 返回形式
   * @returns 分割结果
   */
  async segmentBody(
    imageInput: ImageInput,
    returnForm: ReturnForm = ReturnForm.CUT_OUT
  ): Promise<SegmentResponse> {
    try {
      if (typeof imageInput === 'string') {
        // URL输入
        const request = new ImagesegClient.SegmentBodyRequest({
          imageURL: imageInput,
          returnForm: returnForm
        });
        const runtime = new TeaUtil.RuntimeOptions({});
        const response = await this.client.segmentBodyWithOptions(request, runtime);
        return this.formatResponse(response);
      } else {
        // Buffer或Stream输入
        const request = new ImagesegClient.SegmentBodyAdvanceRequest();
        request.imageURLObject = this.convertToStreamOrResponse(imageInput);
        request.returnForm = returnForm;

        const runtime = new TeaUtil.RuntimeOptions({});
        const response = await this.client.segmentBodyAdvance(request, runtime);
        return this.formatResponse(response);
      }
    } catch (error: any) {
      throw new Error(`Body segmentation failed: ${error.message}`);
    }
  }

  /**
   * 头发分割
   * @param imageInput 图像输入
   * @param returnForm 返回形式
   * @returns 分割结果
   */
  async segmentHair(
    imageInput: ImageInput,
    returnForm: ReturnForm = ReturnForm.CUT_OUT
  ): Promise<SegmentResponse> {
    try {
      if (typeof imageInput === 'string') {
        // URL输入
        const request = new ImagesegClient.SegmentHairRequest({
          imageURL: imageInput,
          returnForm: returnForm
        });
        const runtime = new TeaUtil.RuntimeOptions({});
        const response = await this.client.segmentHairWithOptions(request, runtime);
        return this.formatResponse(response);
      } else {
        // Buffer或Stream输入
        const request = new ImagesegClient.SegmentHairAdvanceRequest();
        request.imageURLObject = this.convertToStreamOrResponse(imageInput);
        request.returnForm = returnForm;

        const runtime = new TeaUtil.RuntimeOptions({});
        const response = await this.client.segmentHairAdvance(request, runtime);
        return this.formatResponse(response);
      }
    } catch (error: any) {
      throw new Error(`Hair segmentation failed: ${error.message}`);
    }
  }

  /**
   * 轮询异步任务结果
   * @param jobId 任务ID
   * @returns 分割结果
   */
  private async pollAsyncResult(jobId: string): Promise<SegmentResponse> {
    const maxRetries = 30; // 最大重试次数
    const retryInterval = 2000; // 重试间隔（毫秒）

    for (let i = 0; i < maxRetries; i++) {
      try {
        const request = new ViapiClient.GetAsyncJobResultRequest({
          jobId: jobId
        });
        const runtime = new TeaUtil.RuntimeOptions({});
        const response = await this.viapiClient.getAsyncJobResultWithOptions(request, runtime);

        // 调试：打印轮询响应
        console.log(`Polling attempt ${i+1}/${maxRetries}:`, JSON.stringify(response.body, null, 2));

        // 检查多种可能的状态字段
        const status = response.body?.data?.status ||
                       response.body?.data?.Status ||
                       response.body?.status ||
                       response.body?.Status;

        if (status === 'SUCCESS' || status === 'success' || status === 'PROCESS_SUCCESS') {
          // 任务成功完成 - 检查多种可能的结果字段
          let imageURL: string | undefined;

          // 首先检查 result 字段是否为 JSON 字符串
          if (response.body?.data?.result && typeof response.body.data.result === 'string') {
            try {
              const resultObj = JSON.parse(response.body.data.result);
              if (resultObj.imageUrl) {
                imageURL = resultObj.imageUrl;
              } else if (resultObj.imageURL) {
                imageURL = resultObj.imageURL;
              } else if (resultObj.url) {
                imageURL = resultObj.url;
              }
            } catch (error) {
              console.error('Failed to parse result JSON:', error);
            }
          }

          // 如果没有从 JSON 字符串中获取到，则检查其他可能的字段
          if (!imageURL) {
            if (response.body?.data?.result?.imageURL) {
              imageURL = response.body.data.result.imageURL;
            } else if (response.body?.data?.result?.ImageURL) {
              imageURL = response.body.data.result.ImageURL;
            } else if (response.body?.data?.result?.url) {
              imageURL = response.body.data.result.url;
            } else if (response.body?.data?.result?.Url) {
              imageURL = response.body.data.result.Url;
            } else if (response.body?.data?.imageURL) {
              imageURL = response.body.data.imageURL;
            } else if (response.body?.data?.ImageURL) {
              imageURL = response.body.data.ImageURL;
            } else if (response.body?.data?.url) {
              imageURL = response.body.data.url;
            } else if (response.body?.data?.Url) {
              imageURL = response.body.data.Url;
            }
          }

          if (!imageURL) {
            console.error('Failed to extract image URL from response:', response.body);
            throw new Error(`Failed to extract image URL from successful job result: ${JSON.stringify(response.body)}`);
          }

          return {
            code: response.body?.code || response.body?.Code || '200',
            message: response.body?.message || response.body?.Message || 'Success',
            requestId: response.body?.requestId || response.body?.RequestId || '',
            data: {
              imageURL: imageURL
            }
          };
        } else if (status === 'FAILED' || status === 'failed' || status === 'FAILURE' || status === 'failure' || status === 'PROCESS_FAILED') {
          // 任务失败 - 检查多种可能的消息字段
          const errorMessage = response.body?.data?.message ||
                              response.body?.data?.Message ||
                              response.body?.message ||
                              response.body?.Message ||
                              'Unknown error';
          throw new Error(`Async job failed: ${errorMessage}`);
        } else if (status === 'RUNNING' || status === 'running' || status === 'PENDING' || status === 'pending' || status === 'PROCESS_RUNNING') {
          // 任务还在进行中，继续轮询
          console.log(`Job ${jobId} is still running (status: ${status}), waiting ${retryInterval}ms before next poll...`);
        } else {
          console.log(`Unknown job status: ${status}, waiting ${retryInterval}ms before next poll...`);
        }

        await new Promise(resolve => setTimeout(resolve, retryInterval));
      } catch (error: any) {
        console.error(`Polling error (attempt ${i+1}/${maxRetries}):`, error);
        if (i === maxRetries - 1) {
          throw new Error(`Polling async result failed: ${error.message}`);
        }
        await new Promise(resolve => setTimeout(resolve, retryInterval));
      }
    }

    throw new Error('Async job timeout: Maximum polling attempts reached');
  }

  /**
   * 通用分割方法 - 根据类型自动选择
   * @param segmentType 分割类型
   * @param imageInput 图像输入
   * @param returnForm 返回形式
   * @returns 分割结果
   */
  async segment(
    segmentType: SegmentType,
    imageInput: ImageInput,
    returnForm: ReturnForm = ReturnForm.CUT_OUT
  ): Promise<SegmentResponse> {
    switch (segmentType) {
      case SegmentType.BODY:
        return this.segmentBody(imageInput, returnForm);
      case SegmentType.HAIR:
        return this.segmentHair(imageInput, returnForm);
      case SegmentType.COMMON:
      default:
        return this.segmentCommon(imageInput, returnForm);
    }
  }

  /**
   * 将Buffer转换为Stream或HTTP Response（根据官方文档）
   * @param input Buffer或Stream
   * @returns Readable Stream或HTTP Response
   */
  private convertToStreamOrResponse(input: Buffer | Readable): any {
    if (input instanceof Readable) {
      return input;
    }

    // 对于Buffer，创建一个Readable Stream
    const stream = new Readable();
    stream.push(input);
    stream.push(null);
    return stream;
  }

  /**
   * 从URL获取HTTP Response（用于Advance请求）
   * @param url 图片URL
   * @returns HTTP Response
   */
  private async getResponseFromUrl(url: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const httpClient = (urlObj.protocol === "https:") ? https : http;

      httpClient.get(url, function (response) {
        resolve(response);
      }).on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * 格式化API响应
   * @param response 原始响应
   * @returns 格式化后的响应
   */
  private formatResponse(response: any): SegmentResponse {
    return {
      code: response.body?.code || '200',
      message: response.body?.message || 'Success',
      requestId: response.body?.requestId || '',
      data: response.body?.data ? {
        imageURL: response.body.data.imageURL || ''
      } : undefined
    };
  }

  /**
   * 获取客户端配置信息
   */
  getConfig(): AliCloudImageSegConfig {
    return { ...this.config };
  }
}

/**
 * 创建优化的阿里云图像分割客户端
 * @param config 配置选项
 * @returns 客户端实例
 */
export function createOptimizedAliCloudClient(config?: AliCloudImageSegConfig): OptimizedAliCloudImageSegClient {
  const defaultConfig: AliCloudImageSegConfig = {
    accessKeyId: process.env.ALICLOUD_ACCESS_KEY_ID || '',
    accessKeySecret: process.env.ALICLOUD_ACCESS_KEY_SECRET || '',
    endpoint: process.env.ALICLOUD_IMAGESEG_ENDPOINT || 'imageseg.cn-shanghai.aliyuncs.com',
    regionId: process.env.ALICLOUD_REGION_ID || 'cn-shanghai',
  };

  if (!defaultConfig.accessKeyId || !defaultConfig.accessKeySecret) {
    throw new Error('AliCloud credentials not found. Please set ALICLOUD_ACCESS_KEY_ID and ALICLOUD_ACCESS_KEY_SECRET environment variables.');
  }

  return new OptimizedAliCloudImageSegClient(config || defaultConfig);
}

// 导出所有类型和枚举
export { ImagesegClient, OpenapiClient, TeaUtil };
