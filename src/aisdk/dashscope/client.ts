export interface DashScopeConfig {
  apiKey: string;
  baseUrl?: string;
}

export interface BackgroundGenerationInput {
  base_image_url: string;
  ref_prompt?: string;
  ref_image_url?: string;
  reference_edge?: {
    foreground_edge?: string[];
    background_edge?: string[];
    foreground_edge_prompt?: string[];
    background_edge_prompt?: string[];
  };
}

export interface BackgroundGenerationParameters {
  model_version?: 'v2' | 'v3';
  n?: number;
  ref_prompt_weight?: number;
  noise_level?: number;
}

export interface BackgroundGenerationRequest {
  model: string;
  input: BackgroundGenerationInput;
  parameters?: BackgroundGenerationParameters;
}

export interface TaskResponse {
  output?: {
    task_id: string;
    task_status: 'PENDING' | 'RUNNING' | 'SUCCEEDED' | 'FAILED';
  };
  usage?: {
    image_count: number;
  };
  request_id: string;
}

export interface TaskResult {
  output?: {
    task_id: string;
    task_status: 'PENDING' | 'RUNNING' | 'SUCCEEDED' | 'FAILED';
    results?: Array<{
      url: string;
    }>;
    task_metrics?: {
      TOTAL: number;
      SUCCEEDED: number;
      FAILED: number;
    };
    message?: string; // Error message when task fails
    code?: string; // Error code when task fails
  };
  usage?: {
    image_count: number;
  };
  request_id: string;
  message?: string; // Top-level error message
  code?: string; // Top-level error code
}

export class DashScopeClient {
  private apiKey: string;
  private baseUrl: string;

  constructor(config: DashScopeConfig) {
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl || 'https://dashscope.aliyuncs.com';
  }

  /**
   * 创建背景生成任务
   */
  async createBackgroundGenerationTask(
    request: BackgroundGenerationRequest
  ): Promise<TaskResponse> {
    try {
      // Log request details for debugging (excluding sensitive data)
      console.log('Creating background generation task with request:', {
        model: request.model,
        input: {
          has_base_image: !!request.input.base_image_url,
          has_ref_prompt: !!request.input.ref_prompt,
          has_ref_image: !!request.input.ref_image_url,
        },
        parameters: request.parameters
      });

      const response = await fetch(
        `${this.baseUrl}/api/v1/services/aigc/background-generation/generation/`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
            'X-DashScope-Async': 'enable',
          },
          body: JSON.stringify(request),
        }
      );

      // Get response text first to ensure we have it for error reporting
      const responseText = await response.text();

      if (!response.ok) {
        console.error('DashScope API error response:', {
          status: response.status,
          statusText: response.statusText,
          responseText,
          url: `${this.baseUrl}/api/v1/services/aigc/background-generation/generation/`,
          requestBody: JSON.stringify(request, null, 2)
        });

        // 特殊处理下载超时错误
        if (responseText.includes('DataInspection') || responseText.includes('timeout')) {
          throw new Error(`Image download timeout: The image server may be unstable or the image is too large. Please try with a smaller image or try again later. (HTTP ${response.status})`);
        }

        throw new Error(`HTTP ${response.status}: ${responseText}`);
      }

      // Parse the response text as JSON
      let result;
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Failed to parse DashScope API response as JSON:', responseText);
        throw new Error(`Invalid JSON response from DashScope API: ${responseText}`);
      }

      // Log successful task creation
      console.log('Background generation task created successfully:', {
        task_id: result.output?.task_id,
        request_id: result.request_id
      });

      return result;
    } catch (error: any) {
      console.error('DashScope createBackgroundGenerationTask failed:', error);
      throw new Error(`Background generation task creation failed: ${error.message}`);
    }
  }

  /**
   * 查询任务结果
   */
  async getTaskResult(taskId: string): Promise<TaskResult> {
    try {
      const response = await fetch(
        `${this.baseUrl}/api/v1/tasks/${taskId}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`DashScope getTaskResult HTTP error for task ${taskId}:`, {
          status: response.status,
          statusText: response.statusText,
          errorText
        });
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const result = await response.json();

      // Log detailed response for failed tasks
      if (result.output?.task_status === 'FAILED') {
        console.error(`DashScope task ${taskId} failed - full response:`, JSON.stringify(result, null, 2));
      }

      return result;
    } catch (error: any) {
      console.error(`DashScope getTaskResult failed for task ${taskId}:`, error);
      throw new Error(`Task result query failed: ${error.message}`);
    }
  }

  /**
   * 轮询等待任务完成
   */
  async waitForTaskCompletion(
    taskId: string,
    maxWaitTime: number = 300000, // 5分钟
    pollInterval: number = 3000 // 3秒
  ): Promise<TaskResult> {
    const startTime = Date.now();
    let attemptCount = 0;

    while (Date.now() - startTime < maxWaitTime) {
      attemptCount++;
      const result = await this.getTaskResult(taskId);

      // Log the polling attempt for debugging
      console.log(`DashScope polling attempt ${attemptCount} for task ${taskId}:`, {
        status: result.output?.task_status,
        request_id: result.request_id,
        task_metrics: result.output?.task_metrics
      });

      if (result.output?.task_status === 'SUCCEEDED') {
        console.log(`DashScope task ${taskId} completed successfully after ${attemptCount} attempts`);
        return result;
      }

      if (result.output?.task_status === 'FAILED') {
        // Extract detailed error information
        const errorMessage = result.output?.message || result.message || 'Unknown error';
        const errorCode = result.output?.code || result.code || 'UNKNOWN_ERROR';
        const taskMetrics = result.output?.task_metrics;

        // Log the full error details for debugging
        console.error(`DashScope task ${taskId} failed:`, {
          errorMessage,
          errorCode,
          taskMetrics,
          fullResponse: result
        });

        // Create a detailed error message
        let detailedError = `Background generation task failed: ${errorMessage}`;
        if (errorCode !== 'UNKNOWN_ERROR') {
          detailedError += ` (Code: ${errorCode})`;
        }
        if (taskMetrics && taskMetrics.FAILED > 0) {
          detailedError += ` - ${taskMetrics.FAILED}/${taskMetrics.TOTAL} images failed`;
        }

        throw new Error(detailedError);
      }

      // Log current status for long-running tasks
      if (attemptCount % 5 === 0) {
        console.log(`DashScope task ${taskId} still ${result.output?.task_status} after ${attemptCount} attempts`);
      }

      // 等待下次轮询
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }

    throw new Error(`Background generation task timeout after ${Math.floor(maxWaitTime / 1000)} seconds (${attemptCount} attempts)`);
  }

  /**
   * 一键生成背景（创建任务 + 等待完成）
   */
  async generateBackground(
    input: BackgroundGenerationInput,
    parameters?: BackgroundGenerationParameters
  ): Promise<string[]> {
    try {
      // Validate input parameters
      if (!input.base_image_url) {
        throw new Error('base_image_url is required for background generation');
      }

      if (!input.ref_prompt && !input.ref_image_url) {
        throw new Error('Either ref_prompt or ref_image_url must be provided for background generation');
      }

      // Log input parameters for debugging
      console.log('Starting background generation with input:', {
        base_image_url: input.base_image_url ? '[URL_PROVIDED]' : '[NO_URL]',
        has_ref_prompt: !!input.ref_prompt,
        ref_prompt_length: input.ref_prompt?.length || 0,
        has_ref_image_url: !!input.ref_image_url,
        model_version: parameters?.model_version || 'v3',
        n: parameters?.n || 1
      });

      // 创建任务
      const taskResponse = await this.createBackgroundGenerationTask({
        model: 'wanx-background-generation-v2',
        input,
        parameters: {
          model_version: 'v3', // 使用最新版本
          n: 1,
          ...parameters,
        },
      });

      if (!taskResponse.output?.task_id) {
        console.error('Failed to create background generation task - no task_id in response:', taskResponse);
        throw new Error('Failed to create background generation task: No task ID returned');
      }

      console.log(`Background generation task created with ID: ${taskResponse.output.task_id}`);

      // 等待任务完成
      const result = await this.waitForTaskCompletion(taskResponse.output.task_id);

      if (!result.output?.results || result.output.results.length === 0) {
        console.error('No results generated for completed task:', {
          task_id: taskResponse.output.task_id,
          task_status: result.output?.task_status,
          task_metrics: result.output?.task_metrics
        });
        throw new Error('Background generation completed but no results were generated');
      }

      const urls = result.output.results.map(r => r.url);
      console.log(`Background generation successful, generated ${urls.length} images`);

      return urls;
    } catch (error: any) {
      // Enhance error with more context
      console.error('Background generation failed:', error);

      // Rethrow with original message to preserve error details
      throw error;
    }
  }
}

export function createDashScopeClient(config?: DashScopeConfig): DashScopeClient {
  const defaultConfig: DashScopeConfig = {
    apiKey: process.env.DASHSCOPE_API_KEY || '',
    baseUrl: process.env.DASHSCOPE_BASE_URL || 'https://dashscope.aliyuncs.com',
  };

  return new DashScopeClient(config || defaultConfig);
}
