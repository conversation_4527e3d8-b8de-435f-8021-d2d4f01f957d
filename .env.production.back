# -----------------------------------------------------------------------------
# Web Information
# -----------------------------------------------------------------------------
NEXT_PUBLIC_WEB_URL = "https://aichangebackground.com"
NEXT_PUBLIC_PROJECT_NAME = "AiChangeBackground"

# -----------------------------------------------------------------------------
# Database with Supabase
# -----------------------------------------------------------------------------
# https://supabase.com/docs/guides/getting-started/quickstarts/nextjs
# Set your Supabase DATABASE_URL

DATABASE_URL = "postgresql://postgres:<EMAIL>:5432/postgres"

# -----------------------------------------------------------------------------
# Auth with next-auth
# https://authjs.dev/getting-started/installation?framework=Next.js
# Set your Auth URL and Secret
# Secret can be generated with `openssl rand -base64 32`
# -----------------------------------------------------------------------------
AUTH_SECRET = "hxhX+md+C3fK2UhjQ+Ek+66D4hnGcxg1E7YObK6j5M8="
AUTH_URL = "https://aichangebackground.com/api/auth"
AUTH_TRUST_HOST = true

# Google Auth
# https://authjs.dev/getting-started/providers/google
AUTH_GOOGLE_ID = "************-k9of3pp27h15k0nhmdstgg0ivr03hra7.apps.googleusercontent.com"
AUTH_GOOGLE_SECRET = "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"
NEXT_PUBLIC_AUTH_GOOGLE_ID = "************-k9of3pp27h15k0nhmdstgg0ivr03hra7.apps.googleusercontent.com"
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED = "true"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED = "false"

# Github Auth
# https://authjs.dev/getting-started/providers/github
AUTH_GITHUB_ID = "********************"
AUTH_GITHUB_SECRET = "****************************************"
NEXT_PUBLIC_AUTH_GITHUB_ENABLED = "false"

# -----------------------------------------------------------------------------
# Analytics with Google Analytics
# https://analytics.google.com
# -----------------------------------------------------------------------------
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID = "G-5ZDB2BF6J1"

# -----------------------------------------------------------------------------
# Analytics with OpenPanel
# https://openpanel.dev
# -----------------------------------------------------------------------------
NEXT_PUBLIC_OPENPANEL_CLIENT_ID = ""

# Analytics with Plausible
# https://plausible.io/
NEXT_PUBLIC_PLAUSIBLE_DOMAIN = ""
NEXT_PUBLIC_PLAUSIBLE_SCRIPT_URL = ""

# -----------------------------------------------------------------------------
# Payment Configuration
# Choose payment provider: 'stripe' or 'paypal' (default: stripe)
# Only one provider can be active at a time
# -----------------------------------------------------------------------------
PAYMENT_PROVIDER = "paypal"

# -----------------------------------------------------------------------------
# Payment with Stripe
# https://docs.stripe.com/keys
# Required when PAYMENT_PROVIDER = "stripe"
# -----------------------------------------------------------------------------
STRIPE_PUBLIC_KEY = ""
STRIPE_PRIVATE_KEY = ""
STRIPE_WEBHOOK_SECRET = ""

# -----------------------------------------------------------------------------
# Payment with PayPal
# https://developer.paypal.com/docs/api/overview/
# Required when PAYMENT_PROVIDER = "paypal"
# -----------------------------------------------------------------------------
PAYPAL_CLIENT_ID = "ActPyCAsoKhSr8hI-zPZYSEvo-q54c_91SHVq2mD7nnAbdg8r6yF1ZheKO43HgeLAIIv0_ZXol0_e9D9"
PAYPAL_CLIENT_SECRET = "EESiTINmr7dxcJlywqBBoxqsuCINIxcUopQJnCRCxfGK0NSeOHJwVj1TTmcf-e7BZcFTP7GOXNhHzf-0"
PAYPAL_ENVIRONMENT = "live"  # sandbox or live
# Optional: For webhook verification (can be left empty for basic setup)
PAYPAL_WEBHOOK_ID = ""
PAYPAL_WEBHOOK_SECRET = ""

# Payment URLs (used by both providers)
NEXT_PUBLIC_PAY_SUCCESS_URL = "https://aichangebackground.com/my-orders"
NEXT_PUBLIC_PAY_FAIL_URL = "https://aichangebackground.com/#pricing"
NEXT_PUBLIC_PAY_CANCEL_URL = "https://aichangebackground.com/#pricing"
NEXT_PUBLIC_LOCALE_DETECTION = "false"

ADMIN_EMAILS = "<EMAIL>,<EMAIL>,<EMAIL>"

NEXT_PUBLIC_DEFAULT_THEME = "light"

# -----------------------------------------------------------------------------
# Storage with aws s3 sdk
# https://docs.aws.amazon.com/s3/index.html
# -----------------------------------------------------------------------------
STORAGE_ENDPOINT = "https://oss-cn-shanghai.aliyuncs.com"
STORAGE_REGION = "oss-cn-shanghai"
STORAGE_ACCESS_KEY = "LTAI5tRk5pfCMawCx6VTYSGf"
STORAGE_SECRET_KEY = "******************************"
STORAGE_BUCKET = "change-background-shipany"
STORAGE_DOMAIN = "https://change-background-shipany.oss-cn-shanghai.aliyuncs.com"

# Google Adsence Code
# https://adsense.com/
NEXT_PUBLIC_GOOGLE_ADCODE = ""

# AliCloud Image Segmentation
ALICLOUD_ACCESS_KEY_ID=LTAI5tRk5pfCMawCx6VTYSGf
ALICLOUD_ACCESS_KEY_SECRET=******************************
ALICLOUD_IMAGESEG_ENDPOINT=imageseg.cn-shanghai.aliyuncs.com
ALICLOUD_REGION_ID=cn-shanghai

# AliCloud DashScope (百炼平台)
DASHSCOPE_API_KEY=sk-156ab3a6bdf84fd7945940e5c5c2830b
DASHSCOPE_BASE_URL=https://dashscope.aliyuncs.com

# Background Provider Configuration
# Choose background provider: 'alicloud' or 'volcengine' (default: alicloud)
BACKGROUND_PROVIDER=volcengine

# VolcEngine (火山引擎) Configuration
# Required when BACKGROUND_PROVIDER=volcengine
VOLCENGINE_API_KEY=7cad8c29-99bb-46b9-8a36-cd1d71ead8f8
ARK_API_KEY=92bc2931-7b29-4edd-8bb2-7fbed10b3728
VOLCENGINE_BASE_URL=https://ark.cn-beijing.volces.com
VOLCENGINE_GUIDANCE_SCALE=5.5
VOLCENGINE_WATERMARK=false
