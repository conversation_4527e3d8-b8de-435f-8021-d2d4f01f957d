{"extends": ["next/core-web-vitals", "next/typescript"], "rules": {"@typescript-eslint/no-require-imports": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": "warn", "prefer-const": "warn", "react/display-name": "warn", "import/no-anonymous-default-export": "warn", "react-hooks/rules-of-hooks": "error", "@next/next/no-html-link-for-pages": "warn", "@next/next/no-img-element": "warn", "jsx-a11y/alt-text": "warn"}}